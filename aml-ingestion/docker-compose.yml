services:
  zookeeper:
    container_name: zookeeper-integrated
    image: confluentinc/cp-zookeeper:7.3.0
    platform: linux/arm64
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - blockchain-aml-network
    healthcheck:
      test: ["CMD", "nc", "-z", "localhost", "2181"]
      interval: 30s
      timeout: 10s
      retries: 3

  kafka:
    container_name: kafka-integrated
    image: confluentinc/cp-kafka:7.3.0
    platform: linux/arm64
    depends_on:
      zookeeper:
        condition: service_healthy
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true"
      KAFKA_LOG_RETENTION_HOURS: 24
      KAFKA_LOG_RETENTION_BYTES: **********
    networks:
      - blockchain-aml-network
    healthcheck:
      test: ["CMD", "kafka-topics", "--bootstrap-server", "localhost:29092", "--list"]
      interval: 30s
      timeout: 10s
      retries: 3

  kafka-ui:
    container_name: kafka-ui-integrated
    image: provectuslabs/kafka-ui:latest
    platform: linux/arm64
    ports:
      - "8080:8080"
    environment:
      KAFKA_CLUSTERS_0_NAME: blockchain-aml
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
      KAFKA_CLUSTERS_0_ZOOKEEPER: zookeeper:2181
    depends_on:
      kafka:
        condition: service_healthy
    networks:
      - blockchain-aml-network

  kafka-init:
    container_name: kafka-init-integrated
    image: confluentinc/cp-kafka:7.3.0
    platform: linux/arm64
    depends_on:
      kafka:
        condition: service_healthy
    command: >
      bash -c "
        echo 'Waiting for Kafka to be ready...'
        cub kafka-ready -b kafka:29092 1 30
        echo 'Creating Kafka topics...'
        kafka-topics --create --if-not-exists --bootstrap-server kafka:29092 --partitions 3 --replication-factor 1 --topic ethereum-transactions
        kafka-topics --create --if-not-exists --bootstrap-server kafka:29092 --partitions 3 --replication-factor 1 --topic filtered-transactions
        kafka-topics --create --if-not-exists --bootstrap-server kafka:29092 --partitions 3 --replication-factor 1 --topic graph-snapshots
        echo 'Kafka topics created successfully'
        kafka-topics --bootstrap-server kafka:29092 --list
      "
    networks:
      - blockchain-aml-network

  # Create additional Kafka topics for CEP processing
  kafka-cep-topics:
    container_name: kafka-cep-topics-init
    image: confluentinc/cp-kafka:7.3.0
    platform: linux/arm64
    depends_on:
      kafka:
        condition: service_healthy
    command: >
      bash -c "
        echo '📋 Creating CEP-specific Kafka topics...'
        kafka-topics --create --if-not-exists --bootstrap-server kafka:29092 --partitions 6 --replication-factor 1 --topic filtered-transactions
        kafka-topics --create --if-not-exists --bootstrap-server kafka:29092 --partitions 3 --replication-factor 1 --topic suspicious-alerts
        kafka-topics --create --if-not-exists --bootstrap-server kafka:29092 --partitions 3 --replication-factor 1 --topic cep-metrics
        echo '✅ CEP topics created successfully'
        kafka-topics --bootstrap-server kafka:29092 --list | grep -E '(filtered|suspicious|cep)'
      "
    networks:
      - blockchain-aml-network

  sepolia-ingestion:
    container_name: sepolia-ingestion-integrated
    build:
      context: ./ingestion        # Tell Docker to use the ingestion folder as build context
      dockerfile: dockerfile      # The dockerfile is located within the ingestion context
    platform: linux/arm64
    depends_on:
      kafka:
        condition: service_healthy
      kafka-init:
        condition: service_completed_successfully
    restart: unless-stopped
    environment:
      - KAFKA_BOOTSTRAP_SERVERS=${KAFKA_BOOTSTRAP_SERVERS:-kafka:29092}
      - INFURA_URL=${INFURA_URL}
      - KAFKA_TRANSACTIONS_TOPIC=ethereum-transactions
      - ETHEREUM_NETWORK=sepolia
      - INGESTION_MODE=live
      - BATCH_SIZE=5
      - LOG_LEVEL=DEBUG
      - INFURA_REQUEST_INTERVAL=1.0
      - INFURA_BATCH_SIZE=3
      - INFURA_MAX_RETRIES=5
      - PYTHONUNBUFFERED=1
    networks:
      - blockchain-aml-network

  flink-jobmanager:
    container_name: flink-jobmanager-cep
    image: flink:1.17-scala_2.12-java11
    platform: linux/arm64
    ports:
      - "8081:8081"
    command: jobmanager
    environment:
      - FLINK_PROPERTIES=jobmanager.rpc.address=flink-jobmanager
      - JOB_MANAGER_RPC_ADDRESS=flink-jobmanager
      # Memory optimization for CEP processing
      - FLINK_CONF_jobmanager_memory_process_size=2048m  
      - FLINK_CONF_taskmanager_memory_process_size=4096m
      # Enable checkpointing for fault tolerance
      - FLINK_CONF_state_backend=filesystem
      - FLINK_CONF_state_checkpoints_dir=file:///tmp/flink-checkpoints
      - FLINK_CONF_execution_checkpointing_interval=30000
      # Parallelism for high throughput
      - FLINK_CONF_parallelism_default=4
    volumes:
      - flink_data:/tmp/flink-checkpoints
    networks:
      - blockchain-aml-network
    depends_on:
      kafka:
        condition: service_healthy

  flink-taskmanager:
    image: flink:1.17-scala_2.12-java11
    platform: linux/arm64
    depends_on:
      - flink-jobmanager
    command: taskmanager
    scale: 2  # Scale for parallel processing
    environment:
      - |
        FLINK_PROPERTIES=
        jobmanager.rpc.address: flink-jobmanager
        taskmanager.numberOfTaskSlots: 8
        taskmanager.memory.process.size: 4096m
        taskmanager.memory.flink.size: 3200m
        state.backend: filesystem
        state.checkpoints.dir: file:///tmp/flink-checkpoints
        parallelism.default: 2
    volumes:
      - flink_data:/tmp/flink-checkpoints
    networks:
      - blockchain-aml-network

  # CEP Processor deployment service
  cep-processor:
    container_name: blockchain-cep-processor
    build:
      context: ./cep
      dockerfile: Dockerfile
    platform: linux/arm64
    depends_on:
      - flink-jobmanager
      - flink-taskmanager
      - kafka
    environment:
      - FLINK_JOBMANAGER_HOST=flink-jobmanager
      - FLINK_JOBMANAGER_PORT=8081
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
      # CEP filtering configuration
      - CEP_FILTERING_TARGET_RATIO=0.85
      - CEP_THROUGHPUT_TARGET=1000
      - CEP_LATENCY_TARGET_MS=200
      # Pattern detection thresholds
      - NORMAL_VALUE_THRESHOLD_ETH=10.0
      - HIGH_VALUE_THRESHOLD_ETH=100.0
      - RAPID_TX_THRESHOLD_MINUTES=2
      - GAS_LIMIT_NORMAL=21000
    networks:
      - blockchain-aml-network
    restart: unless-stopped

  # CEP Monitoring service
  cep-monitor:
    container_name: cep-filtering-monitor
    build:
      context: ./monitoring
      dockerfile: dockerfile
    platform: linux/arm64
    ports:
      - "8082:8082"  # Monitoring dashboard
    environment:
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
      - FLINK_REST_URL=http://flink-jobmanager:8081
      - MONITORING_INTERVAL_SECONDS=30
      - TARGET_FILTERING_RATIO=0.85
    depends_on:
      - flink-jobmanager
      - kafka
      - cep-processor
    networks:
      - blockchain-aml-network

  neo4j:
    container_name: neo4j-graph-db
    image: neo4j:5.15-community
    platform: linux/arm64
    ports:
      - "7474:7474"
      - "7687:7687"
    environment:
      - NEO4J_AUTH=neo4j/blockchain-aml-2024
      - NEO4J_server_memory_heap_initial__size=512M
      - NEO4J_server_memory_heap_max__size=1G
      - NEO4J_server_memory_pagecache_size=256M
      - NEO4J_PLUGINS=["apoc"]
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
    networks:
      - blockchain-aml-network
    healthcheck:
      test: ["CMD", "cypher-shell", "-u", "neo4j", "-p", "blockchain-aml-2024", "RETURN 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  neo4j-ingestion:
    container_name: neo4j-data-ingestion
    build:
      context: ./neo4j
      dockerfile: dockerfile
    environment:
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=blockchain-aml-2024
    depends_on:
      - kafka
      - neo4j
      - cep-processor
    networks:
      - blockchain-aml-network
    restart: unless-stopped

  spark-graph-builder:
    container_name: spark-graph-construction
    build:
      context: ./spark
      dockerfile: Dockerfile
    platform: linux/arm64
    depends_on:
      kafka:
        condition: service_healthy
      neo4j:
        condition: service_healthy
      cep-processor:
        condition: service_started
    environment:
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
      - KAFKA_INPUT_TOPIC=filtered-transactions
      - KAFKA_OUTPUT_TOPIC=graph-snapshots
      - KAFKA_CONSUMER_GROUP=graph-construction-group
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=blockchain-aml-2024
      - SPARK_EXECUTOR_MEMORY=4g
      - SPARK_DRIVER_MEMORY=2g
      - CHECKPOINT_LOCATION=/app/checkpoints
      - ENABLE_DETAILED_METRICS=true
      - WINDOW_DURATION=1 minute
      - SLIDING_DURATION=30 seconds
      - MAX_NODES_PER_GRAPH=8000
      - PYTHONUNBUFFERED=1
    volumes:
      - spark_checkpoints:/tmp/spark-streaming-checkpoint
    networks:
      - blockchain-aml-network
    restart: unless-stopped

volumes:
  flink_data:
    driver: local
  neo4j_data:
    driver: local
  neo4j_logs:
    driver: local
  spark_checkpoints:
    driver: local



networks:
  blockchain-aml-network:
    driver: bridge