flowchart LR
    subgraph TRAINING[Offline Training Phase]
        A[Elliptic Bitcoin Dataset] --> B[Data Preprocessing]
        B --> C[Feature Engineering]
        C --> D[Feature Normalization]
        D --> E[Graph Construction (Nodes, Edges, Timestamps)]
        E --> F[TGAT Model Training]
        F --> G[Trained TGAT Weights]
    end

    subgraph DEPLOYMENT[Online Inference Phase]
        H[Live Ethereum Transactions (Infura)] --> I[Data Preprocessing]
        I --> J[Feature Engineering (Aligned with Elliptic Schema)]
        J --> K[Feature Normalization (Same Scalers as Training)]
        K --> L[Graph Construction (Nodes, Edges, Timestamps)]
        L --> M[TGAT Inference using Trained Weights]
        M --> N[Anomaly Scores & Suspicious Transaction Flags]
    end

    G --> M

    style TRAINING fill:#f5f5f5,stroke:#333,stroke-width:1px
    style DEPLOYMENT fill:#f5f5f5,stroke:#333,stroke-width:1px
    style A fill:#fff8e1
    style H fill:#e1f5fe