"""
Production-Ready Optimized TGAT Training for Elliptic Bitcoin Dataset

This script implements an enhanced TGAT model with:
- Comprehensive checkpointing & versioning
- Advanced metrics including PR-AUC
- Full reproducibility controls
- Production-ready training loop with early stopping
- Model persistence and loading capabilities
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from sklearn.preprocessing import StandardScaler
from sklearn.feature_selection import SelectKBest, mutual_info_classif
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import (roc_auc_score, accuracy_score, precision_score, 
                           recall_score, f1_score, classification_report,
                           average_precision_score, precision_recall_curve)
from sklearn.utils.class_weight import compute_class_weight
import logging
import os
import time
import random
import json
import pickle
from datetime import datetime
from typing import Dict, Tuple, Optional, List
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Import our TGAT model
from tgat_model import TGATForBlockchainAML

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('tgat_training.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def set_reproducibility_seeds(seed: int = 42):
    """Set all random seeds for full reproducibility."""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    
    # Additional reproducibility settings
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    torch.use_deterministic_algorithms(True, warn_only=True)
    
    # Set environment variable for deterministic behavior
    os.environ['PYTHONHASHSEED'] = str(seed)
    os.environ['CUBLAS_WORKSPACE_CONFIG'] = ':4096:8'
    
    logger.info(f"🔒 Reproducibility seeds set to {seed}")

class FocalLoss(nn.Module):
    """Focal Loss for handling class imbalance."""
    
    def __init__(self, alpha: float = 1.0, gamma: float = 2.0, reduction: str = 'mean'):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
        
    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        return focal_loss

class AdaptiveTGATModel(nn.Module):
    """Adaptive TGAT model that can handle variable input dimensions."""
    
    def __init__(self, input_dim: int = 166, hidden_dim: int = 64, 
                 selected_features: int = 64, use_feature_selection: bool = True):
        super(AdaptiveTGATModel, self).__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.selected_features = selected_features
        self.use_feature_selection = use_feature_selection
        
        # Feature selection layer
        if use_feature_selection:
            self.feature_selector = nn.Sequential(
                nn.Linear(input_dim, hidden_dim * 2),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(hidden_dim * 2, selected_features),
                nn.ReLU()
            )
            tgat_input_dim = selected_features
        else:
            self.feature_selector = nn.Linear(input_dim, selected_features)
            tgat_input_dim = selected_features
        
        # Create base TGAT model with adapted dimensions
        self.tgat = TGATForBlockchainAML(
            node_feat_dim=tgat_input_dim,
            edge_feat_dim=8,
            hidden_dim=hidden_dim,
            num_layers=2,
            num_heads=4,
            dropout=0.15
        )
    
    def forward(self, x: torch.Tensor, edge_index: torch.Tensor, 
                edge_attr: torch.Tensor, edge_time: torch.Tensor, 
                node_time: torch.Tensor) -> Dict[str, torch.Tensor]:
        """Forward pass with feature selection."""
        
        # Apply feature selection
        x_selected = self.feature_selector(x)
        
        # Pass through TGAT
        outputs = self.tgat(x_selected, edge_index, edge_attr, edge_time, node_time)
        
        return outputs

class OptimizedEllipticLoader:
    """Optimized Elliptic dataset loader with feature analysis."""
    
    def __init__(self, dataset_path: str = "dataset copy"):
        self.dataset_path = dataset_path
        self.scaler = StandardScaler()
        self.feature_selector = None
        
    def load_and_analyze(self, max_nodes: int = 10000, min_labeled: int = 1000, 
                        selected_features: int = 64) -> Dict:
        """Load data with feature analysis and selection."""
        logger.info("Loading and analyzing Elliptic dataset...")
        
        # Load all data
        features_df = pd.read_csv(f"{self.dataset_path}/elliptic_txs_features.csv", header=None)
        features_df.columns = ['txId'] + [f'feature_{i}' for i in range(1, 167)]
        
        classes_df = pd.read_csv(f"{self.dataset_path}/elliptic_txs_classes.csv")
        edges_df = pd.read_csv(f"{self.dataset_path}/elliptic_txs_edgelist.csv")
        
        logger.info(f"Loaded {len(features_df)} transactions, {len(edges_df)} edges")
        
        # Find labeled transactions first
        labeled_txids = set(classes_df[classes_df['class'] != 'unknown']['txId'])
        
        # Smart sampling: prioritize labeled transactions
        labeled_features = features_df[features_df['txId'].isin(labeled_txids)]
        unlabeled_features = features_df[~features_df['txId'].isin(labeled_txids)]
        
        logger.info(f"Found {len(labeled_features)} labeled transactions")
        
        # Sample data intelligently
        n_labeled = min(len(labeled_features), min_labeled)
        n_unlabeled = min(max_nodes - n_labeled, len(unlabeled_features))
        
        if n_labeled > 0:
            sampled_labeled = labeled_features.sample(n=n_labeled, random_state=42)
        else:
            sampled_labeled = labeled_features
            
        if n_unlabeled > 0:
            sampled_unlabeled = unlabeled_features.sample(n=n_unlabeled, random_state=42)
            final_features = pd.concat([sampled_labeled, sampled_unlabeled])
        else:
            final_features = sampled_labeled
        
        logger.info(f"Using {len(final_features)} transactions ({len(sampled_labeled)} labeled)")
        
        # Create node mapping
        unique_txids = final_features['txId'].unique()
        node_mapping = {txid: idx for idx, txid in enumerate(unique_txids)}
        
        # Process features
        feature_cols = [col for col in final_features.columns if col.startswith('feature_')]
        X = final_features[feature_cols].values.astype(np.float64)
        X = np.nan_to_num(X, nan=0.0)
        
        # Feature selection using Random Forest on labeled data
        if len(sampled_labeled) > 10:  # Need enough samples for feature selection
            labeled_X = sampled_labeled[feature_cols].values.astype(np.float64)
            labeled_X = np.nan_to_num(labeled_X, nan=0.0)
            
            # Get labels for feature selection
            labeled_classes = sampled_labeled.merge(classes_df, on='txId')['class'].values
            labeled_y = (labeled_classes == 1).astype(int)  # 1=illicit, 0=licit
            
            if len(np.unique(labeled_y)) > 1:  # Need both classes
                logger.info("Performing feature selection...")
                self.scaler.fit(labeled_X)
                labeled_X_scaled = self.scaler.transform(labeled_X)
                
                # Use Random Forest for feature selection
                rf = RandomForestClassifier(n_estimators=50, random_state=42, class_weight='balanced')
                rf.fit(labeled_X_scaled, labeled_y)
                
                # Select top features
                feature_importance = rf.feature_importances_
                top_indices = np.argsort(feature_importance)[-selected_features:]
                
                logger.info(f"Selected {len(top_indices)} most important features")
                self.feature_selector = top_indices
            else:
                # Fallback: use all features
                self.scaler.fit(X)
                self.feature_selector = np.arange(min(selected_features, X.shape[1]))
        else:
            # Fallback: use all features
            self.scaler.fit(X)
            self.feature_selector = np.arange(min(selected_features, X.shape[1]))
        
        # Apply scaling and feature selection
        X_scaled = self.scaler.transform(X)
        if self.feature_selector is not None:
            X_selected = X_scaled[:, self.feature_selector]
        else:
            X_selected = X_scaled
        
        # Extract time steps
        time_steps = final_features['feature_1'].values.astype(int)
        
        # Process labels
        merged = final_features[['txId']].merge(classes_df, on='txId', how='left')
        labels = merged['class'].values
        
        binary_labels = np.full(len(labels), -1, dtype=int)
        for i, label in enumerate(labels):
            if pd.isna(label) or label == 'unknown':
                binary_labels[i] = -1
            elif str(label).strip() == '1':
                binary_labels[i] = 1  # illicit
            elif str(label).strip() == '2':
                binary_labels[i] = 0  # licit
        
        labeled_mask = binary_labels != -1
        
        # Process edges
        valid_nodes = set(node_mapping.keys())
        edges_filtered = edges_df[
            edges_df['txId1'].isin(valid_nodes) & 
            edges_df['txId2'].isin(valid_nodes)
        ].copy()
        
        edges_filtered['src'] = edges_filtered['txId1'].map(node_mapping)
        edges_filtered['dst'] = edges_filtered['txId2'].map(node_mapping)
        
        edge_index = np.array([
            edges_filtered['src'].values,
            edges_filtered['dst'].values
        ], dtype=int)
        
        # Enhanced edge features
        edge_features = self.create_enhanced_edge_features(
            X_selected, edge_index, time_steps
        )
        
        # Temporal splits
        unique_times = np.sort(np.unique(time_steps))
        n_times = len(unique_times)
        train_end = int(n_times * 0.6)
        val_end = int(n_times * 0.8)
        
        train_mask = np.isin(time_steps, unique_times[:train_end])
        val_mask = np.isin(time_steps, unique_times[train_end:val_end])
        test_mask = np.isin(time_steps, unique_times[val_end:])
        
        # Convert to tensors
        data = {
            'x': torch.FloatTensor(X_selected),
            'y': torch.LongTensor(binary_labels),
            't': torch.LongTensor(time_steps),
            'edge_index': torch.LongTensor(edge_index),
            'edge_attr': torch.FloatTensor(edge_features),
            'labeled_mask': torch.BoolTensor(labeled_mask),
            'train_mask': torch.BoolTensor(train_mask),
            'val_mask': torch.BoolTensor(val_mask),
            'test_mask': torch.BoolTensor(test_mask),
            'num_nodes': len(node_mapping),
            'num_features': X_selected.shape[1],
            'num_classes': 2,
            'node_mapping': node_mapping
        }
        
        # Calculate class weights
        if labeled_mask.sum() > 0:
            labeled_y = binary_labels[labeled_mask]
            if len(np.unique(labeled_y)) > 1:
                class_weights = compute_class_weight('balanced', classes=np.unique(labeled_y), y=labeled_y)
                class_weight_dict = {0: class_weights[0], 1: class_weights[1]}
            else:
                class_weight_dict = {0: 1.0, 1: 1.0}
        else:
            class_weight_dict = {0: 1.0, 1: 1.0}
        
        data['class_weights'] = class_weight_dict
        
        # Store preprocessing objects for later use
        data['preprocessing'] = {
            'scaler': self.scaler,
            'feature_selector': self.feature_selector
        }
        
        logger.info("✅ Data preprocessing complete!")
        logger.info(f"   Nodes: {data['num_nodes']:,}")
        logger.info(f"   Edges: {data['edge_index'].size(1):,}")
        logger.info(f"   Features: {data['num_features']}")
        logger.info(f"   Labeled nodes: {data['labeled_mask'].sum():,}")
        
        if labeled_mask.sum() > 0:
            train_labeled = (data['labeled_mask'] & data['train_mask']).sum()
            val_labeled = (data['labeled_mask'] & data['val_mask']).sum() 
            test_labeled = (data['labeled_mask'] & data['test_mask']).sum()
            logger.info(f"   Train/Val/Test labeled: {train_labeled}/{val_labeled}/{test_labeled}")
        
        return data
    
    def create_enhanced_edge_features(self, X: np.ndarray, edge_index: np.ndarray, 
                                    timestamps: np.ndarray) -> np.ndarray:
        """Create rich edge features from graph structure."""
        n_edges = edge_index.shape[1]
        edge_features = np.zeros((n_edges, 8))
        
        if n_edges == 0:
            return edge_features
        
        # Node degrees
        n_nodes = X.shape[0]
        in_degree = np.bincount(edge_index[1], minlength=n_nodes)
        out_degree = np.bincount(edge_index[0], minlength=n_nodes)
        
        for i in range(n_edges):
            src, dst = edge_index[0, i], edge_index[1, i]
            
            if src < n_nodes and dst < n_nodes:
                # Degree-based features
                edge_features[i, 0] = out_degree[src] / max(out_degree.max(), 1)
                edge_features[i, 1] = in_degree[dst] / max(in_degree.max(), 1) 
                
                # Temporal features
                time_diff = abs(timestamps[src] - timestamps[dst]) if src < len(timestamps) and dst < len(timestamps) else 0
                edge_features[i, 2] = min(time_diff / 50.0, 1.0)
                
                # Feature similarity
                if X.shape[1] > 0:
                    feat_dim = min(10, X.shape[1])
                    src_feat = X[src, :feat_dim]
                    dst_feat = X[dst, :feat_dim]
                    if np.std(src_feat) > 0 and np.std(dst_feat) > 0:
                        similarity = np.corrcoef(src_feat, dst_feat)[0, 1]
                        edge_features[i, 3] = np.nan_to_num(similarity, nan=0.0)
                
                # Topology features
                edge_features[i, 4] = min(out_degree[src] + in_degree[dst], 100) / 100.0
                edge_features[i, 5] = 1.0 if out_degree[src] > np.mean(out_degree) else 0.0
                edge_features[i, 6] = 1.0 if in_degree[dst] > np.mean(in_degree) else 0.0
                edge_features[i, 7] = (timestamps[src] % 50) / 50.0 if src < len(timestamps) else 0.0
        
        return edge_features

class ProductionTGATTrainer:
    """Production-ready TGAT trainer with comprehensive features."""
    
    def __init__(self, data: Dict, device: str = None, checkpoint_dir: str = "checkpoints"):
        self.data = data
        self.device = device or ('cuda' if torch.cuda.is_available() else 'cpu')
        self.checkpoint_dir = Path(checkpoint_dir)
        self.checkpoint_dir.mkdir(exist_ok=True)
        
        self.model = None
        self.optimizer = None
        self.scheduler = None
        
        # Training state
        self.current_epoch = 0
        self.best_metrics = {
            'f1': 0.0,
            'auc': 0.0,
            'pr_auc': 0.0,
            'epoch': 0
        }
        self.training_history = []
        
        # Use Focal Loss for class imbalance
        self.criterion = FocalLoss(alpha=2.0, gamma=2.0)
        
        # Move data to device
        for key, value in self.data.items():
            if isinstance(value, torch.Tensor):
                self.data[key] = value.to(self.device)
        
        logger.info(f"🖥️  Training on device: {self.device}")
    
    def create_model(self, hidden_dim: int = 64):
        """Create optimized TGAT model."""
        logger.info("Creating production TGAT model...")
        
        self.model = AdaptiveTGATModel(
            input_dim=self.data['num_features'],
            hidden_dim=hidden_dim,
            selected_features=min(64, self.data['num_features']),
            use_feature_selection=True
        ).to(self.device)
        
        # Optimizer with weight decay for regularization
        self.optimizer = optim.AdamW(
            self.model.parameters(), 
            lr=0.001, 
            weight_decay=1e-4,
            eps=1e-8
        )
        
        # Learning rate scheduler - monitors F1 score
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, 
            mode='max', 
            factor=0.5, 
            patience=8, 
            verbose=True,
            min_lr=1e-6
        )
        
        # Count parameters
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        
        logger.info(f"📊 Model Architecture:")
        logger.info(f"   Total parameters: {total_params:,}")
        logger.info(f"   Trainable parameters: {trainable_params:,}")
        logger.info(f"   Model size: {total_params * 4 / 1024 / 1024:.2f} MB")
    
    def compute_comprehensive_metrics(self, mask: torch.Tensor) -> Dict[str, float]:
        """Compute comprehensive evaluation metrics including PR-AUC."""
        self.model.eval()
        
        with torch.no_grad():
            labeled_mask = self.data['labeled_mask'] & mask
            
            if labeled_mask.sum() == 0:
                return {
                    'accuracy': 0.0, 'auc': 0.0, 'pr_auc': 0.0, 'f1': 0.0, 
                    'precision': 0.0, 'recall': 0.0, 'support': 0
                }
            
            try:
                outputs = self.model(
                    self.data['x'],
                    self.data['edge_index'],
                    self.data['edge_attr'],
                    self.data['t'][self.data['edge_index'][0]],
                    self.data['t']
                )
                
                logits = outputs['node_predictions']
            except Exception as e:
                logger.warning(f"Model evaluation failed: {e}")
                return {
                    'accuracy': 0.0, 'auc': 0.0, 'pr_auc': 0.0, 'f1': 0.0, 
                    'precision': 0.0, 'recall': 0.0, 'support': 0
                }
            
            # Get predictions and probabilities
            y_true = self.data['y'][labeled_mask].cpu().numpy()
            y_pred = logits[labeled_mask].argmax(dim=1).cpu().numpy()
            y_prob = torch.softmax(logits[labeled_mask], dim=1)[:, 1].cpu().numpy()
            
            # Calculate comprehensive metrics
            metrics = {
                'support': len(y_true)
            }
            
            # Basic metrics
            metrics['accuracy'] = accuracy_score(y_true, y_pred)
            
            # Handle edge cases for AUC calculations
            if len(np.unique(y_true)) > 1:
                try:
                    # ROC-AUC
                    metrics['auc'] = roc_auc_score(y_true, y_prob)
                    
                    # PR-AUC (Precision-Recall AUC) - better for imbalanced datasets
                    metrics['pr_auc'] = average_precision_score(y_true, y_prob)
                except Exception as e:
                    logger.warning(f"AUC calculation failed: {e}")
                    metrics['auc'] = 0.0
                    metrics['pr_auc'] = 0.0
            else:
                metrics['auc'] = 0.0
                metrics['pr_auc'] = 0.0
            
            # Classification metrics
            try:
                metrics['precision'] = precision_score(y_true, y_pred, average='binary', zero_division=0)
                metrics['recall'] = recall_score(y_true, y_pred, average='binary', zero_division=0)
                metrics['f1'] = f1_score(y_true, y_pred, average='binary', zero_division=0)
            except Exception as e:
                logger.warning(f"Classification metrics failed: {e}")
                metrics['precision'] = 0.0
                metrics['recall'] = 0.0
                metrics['f1'] = 0.0
        
        return metrics
    
    def train_epoch(self) -> float:
        """Execute one training epoch with proper gradient handling."""
        self.model.train()
        
        labeled_train_mask = self.data['labeled_mask'] & self.data['train_mask']
        
        if labeled_train_mask.sum() == 0:
            logger.warning("No labeled training samples available")
            return 0.0
        
        # Zero gradients
        self.optimizer.zero_grad()
        
        try:
            # Forward pass
            outputs = self.model(
                self.data['x'],
                self.data['edge_index'],
                self.data['edge_attr'],
                self.data['t'][self.data['edge_index'][0]],
                self.data['t']
            )
            
            logits = outputs['node_predictions']
            
            # Compute loss
            loss = self.criterion(logits[labeled_train_mask], self.data['y'][labeled_train_mask])
            
            # Backward pass
            loss.backward()
            
            # Gradient clipping for stability
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            # Optimizer step
            self.optimizer.step()
            
            return loss.item()
            
        except Exception as e:
            logger.error(f"Training step failed: {e}")
            return 0.0
    
    def save_checkpoint(self, metrics: Dict[str, float], is_best: bool = False):
        """Save comprehensive model checkpoint with versioning."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Prepare checkpoint data
        checkpoint = {
            'epoch': self.current_epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'metrics': metrics,
            'best_metrics': self.best_metrics,
            'training_history': self.training_history,
            'model_config': {
                'input_dim': self.data['num_features'],
                'hidden_dim': 64,
                'selected_features': min(64, self.data['num_features']),
                'use_feature_selection': True
            },
            'preprocessing': self.data.get('preprocessing', {}),
            'class_weights': self.data.get('class_weights', {}),
            'random_state': 42,
            'timestamp': timestamp
        }
        
        # Regular checkpoint with metrics in filename
        f1_score = metrics.get('f1', 0.0)
        pr_auc = metrics.get('pr_auc', 0.0)
        filename = f"tgat_{timestamp}_epoch{self.current_epoch:03d}_f1{f1_score:.3f}_prauc{pr_auc:.3f}.pt"
        checkpoint_path = self.checkpoint_dir / filename
        
        torch.save(checkpoint, checkpoint_path)
        logger.info(f"💾 Checkpoint saved: {filename}")
        
        # Save best model separately
        if is_best:
            best_path = self.checkpoint_dir / "best_model.pt"
            torch.save(checkpoint, best_path)
            logger.info(f"🏆 Best model updated: F1={f1_score:.4f}, PR-AUC={pr_auc:.4f}")
        
        # Save training metadata
        metadata = {
            'last_checkpoint': filename,
            'best_metrics': self.best_metrics,
            'training_history': self.training_history[-10:],  # Last 10 epochs
            'timestamp': timestamp
        }
        
        with open(self.checkpoint_dir / "training_metadata.json", 'w') as f:
            json.dump(metadata, f, indent=2)
    
    def load_checkpoint(self, checkpoint_path: str):
        """Load model from checkpoint."""
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        
        self.current_epoch = checkpoint['epoch']
        self.best_metrics = checkpoint['best_metrics']
        self.training_history = checkpoint['training_history']
        
        logger.info(f"📂 Checkpoint loaded from epoch {self.current_epoch}")
        return checkpoint
    
    def train(self, epochs: int = 100, patience: int = 20, save_frequency: int = 10):
        """Production training loop with comprehensive monitoring."""
        logger.info(f"🚀 Starting production training for {epochs} epochs")
        logger.info(f"   Early stopping patience: {patience}")
        logger.info(f"   Checkpoint frequency: every {save_frequency} epochs")
        
        patience_counter = 0
        start_time = time.time()
        
        for epoch in range(self.current_epoch, epochs):
            self.current_epoch = epoch
            epoch_start = time.time()
            
            # Training step
            train_loss = self.train_epoch()
            
            # Evaluation every few epochs
            if epoch % 3 == 0 or epoch == epochs - 1:
                # Compute metrics for all splits
                train_metrics = self.compute_comprehensive_metrics(self.data['train_mask'])
                val_metrics = self.compute_comprehensive_metrics(self.data['val_mask'])
                
                # Learning rate scheduling based on validation F1
                old_lr = self.optimizer.param_groups[0]['lr']
                self.scheduler.step(val_metrics['f1'])
                new_lr = self.optimizer.param_groups[0]['lr']
                
                # Record training history
                epoch_record = {
                    'epoch': epoch,
                    'train_loss': train_loss,
                    'train_metrics': train_metrics,
                    'val_metrics': val_metrics,
                    'learning_rate': new_lr,
                    'epoch_time': time.time() - epoch_start
                }
                self.training_history.append(epoch_record)
                
                # Determine if this is the best model (using F1 as primary metric)
                is_best = False
                if val_metrics['f1'] > self.best_metrics['f1']:
                    self.best_metrics = {
                        'f1': val_metrics['f1'],
                        'auc': val_metrics['auc'],
                        'pr_auc': val_metrics['pr_auc'],
                        'epoch': epoch
                    }
                    patience_counter = 0
                    is_best = True
                else:
                    patience_counter += 1
                
                # Logging
                logger.info(
                    f"Epoch {epoch:3d}/{epochs} | "
                    f"Loss: {train_loss:.4f} | "
                    f"Train F1: {train_metrics['f1']:.4f} | "
                    f"Val F1: {val_metrics['f1']:.4f} | "
                    f"Val PR-AUC: {val_metrics['pr_auc']:.4f} | "
                    f"LR: {new_lr:.2e} | "
                    f"Time: {time.time() - epoch_start:.2f}s"
                )
                
                if new_lr != old_lr:
                    logger.info(f"🔄 Learning rate reduced: {old_lr:.2e} → {new_lr:.2e}")
                
                # Save checkpoints
                if epoch % save_frequency == 0 or is_best or epoch == epochs - 1:
                    self.save_checkpoint(val_metrics, is_best=is_best)
                
                # Early stopping check
                if patience_counter >= patience:
                    logger.info(f"🛑 Early stopping triggered at epoch {epoch}")
                    logger.info(f"   No improvement for {patience} epochs")
                    logger.info(f"   Best F1: {self.best_metrics['f1']:.4f} at epoch {self.best_metrics['epoch']}")
                    break
        
        # Training completed
        total_time = time.time() - start_time
        logger.info(f"⏱️  Total training time: {total_time/60:.2f} minutes")
        
        # Load best model for final evaluation
        best_model_path = self.checkpoint_dir / "best_model.pt"
        if best_model_path.exists():
            logger.info("Loading best model for final evaluation...")
            self.load_checkpoint(str(best_model_path))
        
        # Final comprehensive evaluation
        final_metrics = self.evaluate_comprehensive()
        
        return final_metrics
    
    def evaluate_comprehensive(self) -> Dict[str, Dict[str, float]]:
        """Comprehensive evaluation on all data splits."""
        logger.info("🎯 Performing comprehensive final evaluation...")
        
        # Evaluate on all splits
        splits = ['train', 'val', 'test']
        results = {}
        
        for split in splits:
            mask_key = f'{split}_mask'
            if mask_key in self.data:
                metrics = self.compute_comprehensive_metrics(self.data[mask_key])
                results[split] = metrics
                
                logger.info(f"📊 {split.upper()} Results:")
                logger.info(f"   Accuracy: {metrics['accuracy']:.4f}")
                logger.info(f"   ROC-AUC: {metrics['auc']:.4f}")
                logger.info(f"   PR-AUC: {metrics['pr_auc']:.4f}")
                logger.info(f"   F1-Score: {metrics['f1']:.4f}")
                logger.info(f"   Precision: {metrics['precision']:.4f}")
                logger.info(f"   Recall: {metrics['recall']:.4f}")
                logger.info(f"   Support: {metrics['support']:,}")
        
        # Generate detailed classification report for test set
        if 'test' in results and results['test']['support'] > 0:
            self.generate_classification_report()
        
        return results
    
    def generate_classification_report(self):
        """Generate detailed classification report for test set."""
        self.model.eval()
        
        with torch.no_grad():
            test_labeled_mask = self.data['labeled_mask'] & self.data['test_mask']
            
            if test_labeled_mask.sum() == 0:
                return
            
            outputs = self.model(
                self.data['x'],
                self.data['edge_index'],
                self.data['edge_attr'],
                self.data['t'][self.data['edge_index'][0]],
                self.data['t']
            )
            
            logits = outputs['node_predictions']
            y_true = self.data['y'][test_labeled_mask].cpu().numpy()
            y_pred = logits[test_labeled_mask].argmax(dim=1).cpu().numpy()
            
            # Detailed classification report
            report = classification_report(
                y_true, y_pred, 
                target_names=['Licit', 'Illicit'],
                digits=4
            )
            
            logger.info("📋 Detailed Classification Report (Test Set):")
            logger.info(f"\n{report}")
            
            # Save report to file
            report_path = self.checkpoint_dir / "classification_report.txt"
            with open(report_path, 'w') as f:
                f.write("TGAT Classification Report\n")
                f.write("=" * 50 + "\n")
                f.write(f"Generated: {datetime.now()}\n")
                f.write(f"Test samples: {len(y_true):,}\n")
                f.write(f"Best model from epoch: {self.best_metrics['epoch']}\n\n")
                f.write(report)

def save_training_config(config: Dict, checkpoint_dir: Path):
    """Save training configuration for reproducibility."""
    config_path = checkpoint_dir / "training_config.json"
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
    logger.info(f"💾 Training configuration saved: {config_path}")

def main():
    """Main production training function with full reproducibility."""
    print("🚀 Production-Ready TGAT Training on Elliptic Dataset")
    print("=" * 60)
    
    # Training configuration
    config = {
        'random_seed': 42,
        'dataset_config': {
            'max_nodes': 15000,
            'min_labeled': 3000,
            'selected_features': 64
        },
        'model_config': {
            'hidden_dim': 64,
            'use_feature_selection': True
        },
        'training_config': {
            'epochs': 100,
            'patience': 20,
            'save_frequency': 10,
            'learning_rate': 0.001,
            'weight_decay': 1e-4
        },
        'focal_loss_config': {
            'alpha': 2.0,
            'gamma': 2.0
        }
    }
    
    # Set reproducibility
    set_reproducibility_seeds(config['random_seed'])
    
    # Create checkpoint directory
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    checkpoint_dir = Path(f"checkpoints/tgat_run_{timestamp}")
    checkpoint_dir.mkdir(parents=True, exist_ok=True)
    
    # Save configuration
    save_training_config(config, checkpoint_dir)
    
    if not os.path.exists("dataset copy"):
        logger.error("❌ Dataset directory 'dataset copy' not found!")
        print("Please ensure the Elliptic dataset is available in 'dataset copy' directory")
        return
    
    try:
        # Load and prepare data
        logger.info("📊 Loading and optimizing dataset...")
        loader = OptimizedEllipticLoader()
        data = loader.load_and_analyze(**config['dataset_config'])
        
        if data['labeled_mask'].sum() == 0:
            logger.error("❌ No labeled data found!")
            return
        
        # Print dataset statistics
        labeled_count = data['labeled_mask'].sum()
        illicit_count = (data['y'][data['labeled_mask']] == 1).sum()
        
        logger.info("📈 Dataset Statistics:")
        logger.info(f"   Total nodes: {data['num_nodes']:,}")
        logger.info(f"   Total edges: {data['edge_index'].size(1):,}")
        logger.info(f"   Features: {data['num_features']}")
        logger.info(f"   Labeled nodes: {labeled_count:,}")
        logger.info(f"   Illicit transactions: {illicit_count:,} ({illicit_count/labeled_count*100:.1f}%)")
        logger.info(f"   Class imbalance ratio: {(labeled_count-illicit_count)/illicit_count:.2f}:1")
        
        # Create trainer
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        logger.info(f"🤖 Creating production TGAT model (device: {device})...")
        
        trainer = ProductionTGATTrainer(data, device=device, checkpoint_dir=str(checkpoint_dir))
        trainer.create_model(**config['model_config'])
        
        # Start training
        logger.info("🏋️ Starting production training...")
        results = trainer.train(**config['training_config'])
        
        # Training summary
        logger.info("\n🎉 Production Training Complete!")
        logger.info("=" * 50)
        
        if 'test' in results:
            test_results = results['test']
            logger.info("🏆 Final Test Performance:")
            logger.info(f"   Accuracy: {test_results['accuracy']:.4f}")
            logger.info(f"   ROC-AUC: {test_results['auc']:.4f}")
            logger.info(f"   PR-AUC: {test_results['pr_auc']:.4f} (primary metric for imbalanced data)")
            logger.info(f"   F1-Score: {test_results['f1']:.4f}")
            logger.info(f"   Precision: {test_results['precision']:.4f}")
            logger.info(f"   Recall: {test_results['recall']:.4f}")
        
        logger.info(f"\n📁 All outputs saved to: {checkpoint_dir}")
        logger.info("   - Model checkpoints")
        logger.info("   - Training configuration")
        logger.info("   - Training history")
        logger.info("   - Classification report")
        logger.info("   - Training metadata")
        
        # Generate final summary report
        summary = {
            'experiment_id': timestamp,
            'config': config,
            'results': results,
            'best_metrics': trainer.best_metrics,
            'checkpoint_dir': str(checkpoint_dir),
            'completion_time': datetime.now().isoformat()
        }
        
        summary_path = checkpoint_dir / "experiment_summary.json"
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
        
        logger.info(f"📋 Experiment summary saved: {summary_path}")
        
    except Exception as e:
        logger.error(f"❌ Production training failed: {e}")
        import traceback
        traceback.print_exc()
        raise

def load_trained_model(checkpoint_path: str, device: str = None):
    """Utility function to load a trained model for inference."""
    device = device or ('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Load checkpoint
    checkpoint = torch.load(checkpoint_path, map_location=device)
    
    # Recreate model
    model_config = checkpoint['model_config']
    model = AdaptiveTGATModel(**model_config).to(device)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    # Load preprocessing objects
    preprocessing = checkpoint.get('preprocessing', {})
    
    logger.info(f"✅ Model loaded from {checkpoint_path}")
    logger.info(f"   Epoch: {checkpoint['epoch']}")
    logger.info(f"   Best F1: {checkpoint['best_metrics']['f1']:.4f}")
    
    return {
        'model': model,
        'preprocessing': preprocessing,
        'config': model_config,
        'metrics': checkpoint['best_metrics'],
        'device': device
    }

if __name__ == "__main__":
    main()