# Prometheus配置文件
# Prometheus Configuration for AML Pipeline Monitoring

global:
  scrape_interval: 15s
  evaluation_interval: 15s

# 告警规则文件
rule_files:
  - "alert_rules.yml"

# 告警管理器配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# 监控目标配置
scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # 系统指标监控
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  # 容器指标监控
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']

  # Kafka指标监控
  - job_name: 'kafka'
    static_configs:
      - targets: ['kafka:9092']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Flink指标监控
  - job_name: 'flink-jobmanager'
    static_configs:
      - targets: ['flink-jobmanager:8081']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Neo4j指标监控
  - job_name: 'neo4j'
    static_configs:
      - targets: ['neo4j:7474']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # 自定义应用指标
  - job_name: 'neo4j-ingestion'
    static_configs:
      - targets: ['neo4j-ingestion:8000']
    metrics_path: '/metrics'
    scrape_interval: 15s

  # CEP处理器指标
  - job_name: 'cep-processor'
    static_configs:
      - targets: ['cep-processor:8081']
    metrics_path: '/metrics'
    scrape_interval: 30s