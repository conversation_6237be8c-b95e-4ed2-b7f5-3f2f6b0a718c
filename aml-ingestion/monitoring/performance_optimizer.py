#!/usr/bin/env python3
"""
AML系统性能优化器
AML System Performance Optimizer

基于实时指标自动调优系统参数
"""

import time
import logging
import json
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import requests
import psutil
import docker

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    timestamp: float
    cpu_usage: float
    memory_usage: float
    disk_io: Dict[str, float]
    network_io: Dict[str, float]
    kafka_throughput: float
    neo4j_query_time: float
    flink_processing_rate: float
    transaction_latency: float

class PerformanceOptimizer:
    """性能优化器"""
    
    def __init__(self):
        self.docker_client = docker.from_env()
        self.prometheus_url = "http://localhost:9090"
        self.optimization_history: List[Dict] = []
        
        # 性能阈值配置
        self.thresholds = {
            'cpu_high': 80.0,
            'memory_high': 85.0,
            'kafka_lag_high': 1000,
            'neo4j_query_slow': 1.0,
            'transaction_latency_high': 5.0
        }
        
        # 优化参数配置
        self.optimization_params = {
            'neo4j_batch_size': [50, 100, 200, 500],
            'neo4j_heap_size': ['2G', '3G', '4G', '6G'],
            'flink_parallelism': [1, 2, 4, 8],
            'kafka_batch_size': [16384, 32768, 65536, 131072]
        }
    
    def collect_system_metrics(self) -> PerformanceMetrics:
        """收集系统性能指标"""
        try:
            # CPU和内存使用率
            cpu_usage = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            memory_usage = memory.percent
            
            # 磁盘I/O
            disk_io = psutil.disk_io_counters()._asdict() if psutil.disk_io_counters() else {}
            
            # 网络I/O
            network_io = psutil.net_io_counters()._asdict() if psutil.net_io_counters() else {}
            
            # 从Prometheus获取应用指标
            app_metrics = self.query_prometheus_metrics()
            
            return PerformanceMetrics(
                timestamp=time.time(),
                cpu_usage=cpu_usage,
                memory_usage=memory_usage,
                disk_io=disk_io,
                network_io=network_io,
                kafka_throughput=app_metrics.get('kafka_throughput', 0.0),
                neo4j_query_time=app_metrics.get('neo4j_query_time', 0.0),
                flink_processing_rate=app_metrics.get('flink_processing_rate', 0.0),
                transaction_latency=app_metrics.get('transaction_latency', 0.0)
            )
            
        except Exception as e:
            logger.error(f"收集系统指标失败: {e}")
            return None
    
    def query_prometheus_metrics(self) -> Dict[str, float]:
        """从Prometheus查询应用指标"""
        metrics = {}
        
        queries = {
            'kafka_throughput': 'rate(kafka_server_brokertopicmetrics_messagesin_total[5m])',
            'neo4j_query_time': 'neo4j_cypher_query_duration_seconds',
            'flink_processing_rate': 'flink_taskmanager_job_task_numRecordsInPerSecond',
            'transaction_latency': 'histogram_quantile(0.95, rate(transaction_processing_duration_seconds_bucket[5m]))'
        }
        
        for metric_name, query in queries.items():
            try:
                response = requests.get(
                    f"{self.prometheus_url}/api/v1/query",
                    params={'query': query},
                    timeout=5
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data['data']['result']:
                        value = float(data['data']['result'][0]['value'][1])
                        metrics[metric_name] = value
                        
            except Exception as e:
                logger.warning(f"查询Prometheus指标 {metric_name} 失败: {e}")
                metrics[metric_name] = 0.0
        
        return metrics
    
    def analyze_performance_bottlenecks(self, metrics: PerformanceMetrics) -> List[str]:
        """分析性能瓶颈"""
        bottlenecks = []
        
        # CPU瓶颈检测
        if metrics.cpu_usage > self.thresholds['cpu_high']:
            bottlenecks.append(f"CPU使用率过高: {metrics.cpu_usage:.1f}%")
        
        # 内存瓶颈检测
        if metrics.memory_usage > self.thresholds['memory_high']:
            bottlenecks.append(f"内存使用率过高: {metrics.memory_usage:.1f}%")
        
        # Neo4j查询性能
        if metrics.neo4j_query_time > self.thresholds['neo4j_query_slow']:
            bottlenecks.append(f"Neo4j查询延迟过高: {metrics.neo4j_query_time:.2f}s")
        
        # 事务处理延迟
        if metrics.transaction_latency > self.thresholds['transaction_latency_high']:
            bottlenecks.append(f"事务处理延迟过高: {metrics.transaction_latency:.2f}s")
        
        return bottlenecks
    
    def optimize_neo4j_performance(self, metrics: PerformanceMetrics) -> Dict[str, str]:
        """优化Neo4j性能"""
        optimizations = {}
        
        # 如果查询延迟高，调整批处理大小
        if metrics.neo4j_query_time > self.thresholds['neo4j_query_slow']:
            current_batch_size = self.get_current_neo4j_batch_size()
            if current_batch_size < 500:
                new_batch_size = min(500, current_batch_size * 2)
                optimizations['batch_size'] = str(new_batch_size)
                logger.info(f"调整Neo4j批处理大小: {current_batch_size} -> {new_batch_size}")
        
        # 如果内存使用率高，调整堆大小
        if metrics.memory_usage > self.thresholds['memory_high']:
            optimizations['heap_optimization'] = "减少堆内存分配"
            logger.info("建议减少Neo4j堆内存分配")
        
        return optimizations
    
    def optimize_flink_performance(self, metrics: PerformanceMetrics) -> Dict[str, str]:
        """优化Flink性能"""
        optimizations = {}
        
        # 如果处理速率低，调整并行度
        if metrics.flink_processing_rate < 100:  # 每秒处理少于100条记录
            optimizations['parallelism'] = "增加并行度"
            logger.info("建议增加Flink并行度")
        
        # 如果CPU使用率高，考虑资源调整
        if metrics.cpu_usage > self.thresholds['cpu_high']:
            optimizations['resource_allocation'] = "调整CPU资源分配"
            logger.info("建议调整Flink CPU资源分配")
        
        return optimizations
    
    def get_current_neo4j_batch_size(self) -> int:
        """获取当前Neo4j批处理大小"""
        try:
            container = self.docker_client.containers.get('neo4j-data-ingestion')
            # 这里可以通过环境变量或配置文件获取当前批处理大小
            return 100  # 默认值
        except Exception:
            return 100
    
    def apply_optimizations(self, optimizations: Dict[str, Dict[str, str]]) -> bool:
        """应用优化配置"""
        try:
            success = True
            
            for service, params in optimizations.items():
                logger.info(f"应用 {service} 优化: {params}")
                
                if service == 'neo4j':
                    success &= self.apply_neo4j_optimizations(params)
                elif service == 'flink':
                    success &= self.apply_flink_optimizations(params)
            
            return success
            
        except Exception as e:
            logger.error(f"应用优化失败: {e}")
            return False
    
    def apply_neo4j_optimizations(self, params: Dict[str, str]) -> bool:
        """应用Neo4j优化"""
        try:
            # 这里可以通过重启容器或动态配置来应用优化
            if 'batch_size' in params:
                # 更新环境变量或配置文件
                logger.info(f"应用Neo4j批处理大小优化: {params['batch_size']}")
            
            return True
        except Exception as e:
            logger.error(f"应用Neo4j优化失败: {e}")
            return False
    
    def apply_flink_optimizations(self, params: Dict[str, str]) -> bool:
        """应用Flink优化"""
        try:
            # 这里可以通过Flink REST API来调整作业参数
            if 'parallelism' in params:
                logger.info(f"应用Flink并行度优化: {params['parallelism']}")
            
            return True
        except Exception as e:
            logger.error(f"应用Flink优化失败: {e}")
            return False
    
    def generate_performance_report(self, metrics: PerformanceMetrics, bottlenecks: List[str]) -> Dict:
        """生成性能报告"""
        report = {
            'timestamp': metrics.timestamp,
            'system_health': {
                'cpu_usage': f"{metrics.cpu_usage:.1f}%",
                'memory_usage': f"{metrics.memory_usage:.1f}%",
                'status': 'healthy' if len(bottlenecks) == 0 else 'needs_attention'
            },
            'application_performance': {
                'kafka_throughput': f"{metrics.kafka_throughput:.2f} msg/s",
                'neo4j_query_time': f"{metrics.neo4j_query_time:.3f}s",
                'flink_processing_rate': f"{metrics.flink_processing_rate:.2f} records/s",
                'transaction_latency': f"{metrics.transaction_latency:.3f}s"
            },
            'identified_bottlenecks': bottlenecks,
            'recommendations': self.generate_recommendations(bottlenecks)
        }
        
        return report
    
    def generate_recommendations(self, bottlenecks: List[str]) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        for bottleneck in bottlenecks:
            if "CPU" in bottleneck:
                recommendations.append("考虑增加CPU资源或优化算法复杂度")
            elif "内存" in bottleneck:
                recommendations.append("考虑增加内存或优化内存使用")
            elif "Neo4j" in bottleneck:
                recommendations.append("调整Neo4j批处理大小或索引策略")
            elif "延迟" in bottleneck:
                recommendations.append("优化数据处理管道或增加并行度")
        
        if not recommendations:
            recommendations.append("系统性能良好，继续监控")
        
        return recommendations
    
    def run_optimization_cycle(self) -> Dict:
        """运行一次优化周期"""
        logger.info("开始性能优化周期...")
        
        # 1. 收集指标
        metrics = self.collect_system_metrics()
        if not metrics:
            return {'error': '无法收集性能指标'}
        
        # 2. 分析瓶颈
        bottlenecks = self.analyze_performance_bottlenecks(metrics)
        
        # 3. 生成优化方案
        neo4j_opts = self.optimize_neo4j_performance(metrics)
        flink_opts = self.optimize_flink_performance(metrics)
        
        optimizations = {}
        if neo4j_opts:
            optimizations['neo4j'] = neo4j_opts
        if flink_opts:
            optimizations['flink'] = flink_opts
        
        # 4. 应用优化
        if optimizations:
            success = self.apply_optimizations(optimizations)
            if success:
                logger.info("优化应用成功")
            else:
                logger.warning("优化应用部分失败")
        
        # 5. 生成报告
        report = self.generate_performance_report(metrics, bottlenecks)
        report['applied_optimizations'] = optimizations
        
        # 6. 记录历史
        self.optimization_history.append({
            'timestamp': time.time(),
            'report': report,
            'optimizations': optimizations
        })
        
        logger.info("性能优化周期完成")
        return report

def main():
    """主函数"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    optimizer = PerformanceOptimizer()
    
    # 运行持续优化
    while True:
        try:
            report = optimizer.run_optimization_cycle()
            print(json.dumps(report, indent=2, ensure_ascii=False))
            
            # 每5分钟运行一次优化
            time.sleep(300)
            
        except KeyboardInterrupt:
            logger.info("优化器停止")
            break
        except Exception as e:
            logger.error(f"优化周期异常: {e}")
            time.sleep(60)  # 异常时等待1分钟再重试

if __name__ == "__main__":
    main()