# graph/spark_streaming.py - Core Spark Streaming Pipeline

from pyspark.sql import SparkSession
from pyspark.sql.functions import *
from pyspark.sql.types import *
from pyspark.sql.streaming import StreamingQuery
import torch
import torch_geometric
import os
from neo4j import GraphDatabase
import json
import logging

class TransactionGraphStreaming:
    def __init__(self, kafka_config, neo4j_config, colab_config):
        self.kafka_config = kafka_config
        self.neo4j_config = neo4j_config
        self.colab_config = colab_config
        self.spark = self._create_spark_session()
        self.logger = logging.getLogger(__name__)
        
    def _create_spark_session(self):
        """Initialize Spark session with Neo4j connector"""
        return SparkSession.builder \
            .appName("RealTime-AML-GraphConstruction") \
            .config("spark.jars.packages", 
                    "org.neo4j:neo4j-connector-apache-spark_2.12:5.0.1,"
                    "org.apache.spark:spark-sql-kafka-0-10_2.12:3.5.0") \
            .config("spark.streaming.stopGracefullyOnShutdown", "true") \
            .config("spark.sql.streaming.checkpointLocation", "/tmp/spark-checkpoint") \
            .getOrCreate()
    
    def create_transaction_schema(self):
        """Define schema for incoming transaction data from CEP (camelCase format)"""
        return StructType([
            StructField("txId", StringType(), True),
            StructField("fromAddr", StringType(), True),
            StructField("toAddr", StringType(), True),
            StructField("valueEth", DoubleType(), True),
            StructField("gasPrice", LongType(), True),
            StructField("gasUsed", LongType(), True),
            StructField("blockNumber", LongType(), True),
            StructField("timestamp", LongType(), True)
        ])
    
    def read_kafka_stream(self):
        """Read streaming data from Kafka"""
        return self.spark \
            .readStream \
            .format("kafka") \
            .option("kafka.bootstrap.servers", self.kafka_config["bootstrap_servers"]) \
            .option("subscribe", self.kafka_config["topic"]) \
            .option("kafka.group.id", self.kafka_config["consumer_group"]) \
            .option("startingOffsets", "earliest") \
            .load()
    
    def process_transactions(self, kafka_df):
        """Process raw Kafka messages into structured transactions"""
        schema = self.create_transaction_schema()
        
        # Parse JSON messages from Kafka
        transactions_df = kafka_df.select(
            col("timestamp").alias("kafka_timestamp"),
            from_json(col("value").cast("string"), schema).alias("transaction")
        ).select("kafka_timestamp", "transaction.*")
        
        return transactions_df
    
    def create_sliding_windows(self, transactions_df):
        """Create 1-minute sliding windows for graph construction"""
        windowed_df = transactions_df \
            .withWatermark("timestamp", "30 seconds") \
            .groupBy(
                window(col("timestamp"), "1 minute", "30 seconds"),
                col("from_address"),
                col("to_address")
            ) \
            .agg(
                count("transaction_hash").alias("transaction_count"),
                sum("value").alias("total_value"),
                avg("gas_price").alias("avg_gas_price"),
                collect_list("transaction_hash").alias("transaction_hashes"),
                first("block_number").alias("window_block_start"),
                last("block_number").alias("window_block_end")
            ) \
            .select(
                col("window.start").alias("window_start"),
                col("window.end").alias("window_end"),
                col("from_address"),
                col("to_address"),
                col("transaction_count"),
                col("total_value"),
                col("avg_gas_price"),
                col("transaction_hashes"),
                col("window_block_start"),
                col("window_block_end")
            )
        
        return windowed_df
    
    def construct_graph_features(self, windowed_df):
        """Convert windowed transactions to graph representation"""
        graph_df = windowed_df.select(
            col("window_start"),
            col("window_end"),
            struct(
                col("from_address").alias("source"),
                col("to_address").alias("target"),
                col("transaction_count").alias("edge_weight"),
                col("total_value").alias("edge_value"),
                col("avg_gas_price").alias("edge_gas_price")
            ).alias("edge"),
            col("transaction_hashes")
        )
        
        return graph_df
    
    def export_to_neo4j(self, graph_batch_df, batch_id):
        """Export graph data to Neo4j using Spark Connector"""
        try:
            # Configure Neo4j write
            graph_batch_df.write \
                .format("org.neo4j.spark.DataSource") \
                .mode("append") \
                .option("url", self.neo4j_config["uri"]) \
                .option("authentication.basic.username", self.neo4j_config["username"]) \
                .option("authentication.basic.password", self.neo4j_config["password"]) \
                .option("labels", "Transaction") \
                .option("node.keys", "window_start,from_address,to_address") \
                .save()
                
            self.logger.info(f"Successfully exported batch {batch_id} to Neo4j")
            
        except Exception as e:
            self.logger.error(f"Failed to export batch {batch_id} to Neo4j: {str(e)}")
    
    def export_to_pytorch_geometric(self, graph_batch_df, batch_id):
        """Convert and export graph data to PyTorch Geometric format"""
        try:
            # Collect graph data (small batches only)
            graph_data = graph_batch_df.collect()
            
            if not graph_data:
                return
            
            # Build PyG graph
            edge_index = []
            edge_attr = []
            node_mapping = {}
            node_idx = 0
            
            for row in graph_data:
                source = row.edge.source
                target = row.edge.target
                
                # Create node mapping
                if source not in node_mapping:
                    node_mapping[source] = node_idx
                    node_idx += 1
                if target not in node_mapping:
                    node_mapping[target] = node_idx
                    node_idx += 1
                
                # Add edge
                edge_index.append([node_mapping[source], node_mapping[target]])
                edge_attr.append([
                    row.edge.edge_weight,
                    row.edge.edge_value,
                    row.edge.edge_gas_price
                ])
            
            # Create PyG data object
            pyg_data = {
                'edge_index': torch.tensor(edge_index).t().contiguous(),
                'edge_attr': torch.tensor(edge_attr, dtype=torch.float),
                'num_nodes': len(node_mapping),
                'batch_id': batch_id,
                'window_start': graph_data[0].window_start.isoformat(),
                'window_end': graph_data[0].window_end.isoformat()
            }
            
            # Send to Colab API (implement API client)
            self._send_to_colab_api(pyg_data)
            
            self.logger.info(f"Successfully exported batch {batch_id} to PyTorch Geometric")
            
        except Exception as e:
            self.logger.error(f"Failed to export batch {batch_id} to PyG: {str(e)}")
    
    def _send_to_colab_api(self, pyg_data):
        """Send graph data to Google Colab API for TGAT inference"""
        # TODO: Implement REST API call to Colab endpoint
        # This will be implemented in Phase 3
        pass
    
    def process_graph_batch(self, graph_batch_df, batch_id):
        """Process each micro-batch for dual export"""
        # Export to Neo4j for persistence
        self.export_to_neo4j(graph_batch_df, batch_id)
        
        # Export to PyG for ML inference
        self.export_to_pytorch_geometric(graph_batch_df, batch_id)
    
    def start_streaming(self):
        """Start the streaming pipeline"""
        self.logger.info("Starting real-time graph construction pipeline...")
        
        # Read from Kafka
        kafka_stream = self.read_kafka_stream()
        
        # Process transactions
        transactions = self.process_transactions(kafka_stream)
        
        # Create sliding windows
        windowed_transactions = self.create_sliding_windows(transactions)
        
        # Construct graph features
        graph_stream = self.construct_graph_features(windowed_transactions)
        
        # Start streaming query with dual export
        query = graph_stream.writeStream \
            .outputMode("append") \
            .foreachBatch(self.process_graph_batch) \
            .option("checkpointLocation", "/tmp/spark-streaming-checkpoint") \
            .trigger(processingTime='30 seconds') \
            .start()
        
        self.logger.info("Graph construction pipeline started successfully")
        return query

# Configuration and startup
if __name__ == "__main__":
    # Configuration
    kafka_config = {
        "bootstrap_servers": os.environ["KAFKA_BOOTSTRAP_SERVERS"],
        "topic": os.environ["KAFKA_INPUT_TOPIC"]
    }
    
    neo4j_config = {
        "uri": "bolt://localhost:7687",
        "username": "neo4j",
        "password": "your_password"
    }
    
    colab_config = {
        "api_endpoint": "https://your-colab-api.com/inference",
        "api_key": "your_api_key"
    }
    
    # Start pipeline
    pipeline = TransactionGraphStreaming(kafka_config, neo4j_config, colab_config)
    streaming_query = pipeline.start_streaming()
    
    # Wait for termination
    streaming_query.awaitTermination()