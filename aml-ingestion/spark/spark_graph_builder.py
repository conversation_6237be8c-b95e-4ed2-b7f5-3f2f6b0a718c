"""
Fixed Spark Graph Builder - Column Reference Issue Resolved
Fixes the UNRESOLVED_COLUMN error by properly referencing parsed columns
"""

import sys
import os
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import pandas as pd
import numpy as np

# Add path for config imports
if '__file__' in globals():
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
else:
    sys.path.append('/app')

from pyspark.sql import SparkSession, DataFrame
import pyspark.sql.functions as F
from pyspark.sql.functions import (
    col, from_json, to_json, struct, window, 
    collect_list, first, last, count, sum as spark_sum,
    avg, max as spark_max, min as spark_min,
    current_timestamp, unix_timestamp, from_unixtime, explode,
    when, isnan, isnull, size, lit, to_timestamp
)
from pyspark.sql.types import (
    <PERSON>ructType, StructField, StringType, DoubleType, 
    LongType, BooleanType, TimestampType, ArrayType
)
from pyspark.sql.streaming.query import StreamingQuery

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class GraphLayerSettings:
    """Configuration settings for graph construction layer"""
    
    def __init__(self):
        # Kafka settings
        self.kafka_bootstrap_servers = os.getenv("KAFKA_BOOTSTRAP_SERVERS", "kafka:29092")
        self.kafka_input_topic = os.getenv("KAFKA_INPUT_TOPIC", "ethereum-transactions")
        self.kafka_output_topic = os.getenv("KAFKA_OUTPUT_TOPIC", "graph-snapshots")
        self.kafka_consumer_group = os.getenv("KAFKA_CONSUMER_GROUP", "graph-construction-group")
        
        # Spark settings
        self.spark_executor_memory = os.getenv("SPARK_EXECUTOR_MEMORY", "1g")
        self.spark_driver_memory = os.getenv("SPARK_DRIVER_MEMORY", "512m")
        self.max_offsets_per_trigger = int(os.getenv("MAX_OFFSETS_PER_TRIGGER", "1000"))
        self.trigger_processing_time = os.getenv("TRIGGER_PROCESSING_TIME", "30 seconds")
        
        # Window settings
        self.window_duration = os.getenv("WINDOW_DURATION", "5 minute")
        self.sliding_duration = os.getenv("SLIDING_DURATION", "1 seconds")
        self.watermark_delay = os.getenv("WATERMARK_DELAY", "2 minutes")
        
        # Neo4j settings
        self.neo4j_uri = os.getenv("NEO4J_URI", "bolt://neo4j:7687")
        self.neo4j_user = os.getenv("NEO4J_USER", "neo4j")
        self.neo4j_password = os.getenv("NEO4J_PASSWORD", "blockchain-aml-2024")
        
        # Performance settings
        self.enable_detailed_metrics = os.getenv("ENABLE_DETAILED_METRICS", "true").lower() == "true"
        self.batch_size = int(os.getenv("BATCH_SIZE", "1000"))
        
        # Output settings
        self.output_format = os.getenv("OUTPUT_FORMAT", "both")
        self.file_output_path = os.getenv("FILE_OUTPUT_PATH", "/tmp/graph-snapshots")
        
        # Checkpoint settings
        self.checkpoint_location = os.getenv("CHECKPOINT_LOCATION", "/tmp/spark-checkpoints")


class GraphSnapshot:
    """Graph snapshot data structure"""
    
    def __init__(self, graph_data=None, window_start=None, window_end=None, 
                 construction_time_ms=0, processing_stats=None, num_nodes=0, num_edges=0):
        self.graph_data = graph_data
        self.window_start = window_start
        self.window_end = window_end
        self.construction_time_ms = construction_time_ms
        self.processing_stats = processing_stats or {}
        self.num_nodes = num_nodes
        self.num_edges = num_edges


class Context7GraphConstructor:
    """Graph constructor for demonstration"""
    
    def __init__(self, settings):
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        self.index_to_address = {}
        
    def build_graph_from_transactions(self, tx_df, window_start, window_end):
        """Build graph from transactions DataFrame"""
        try:
            import torch
            
            # Get unique addresses
            from_addresses = set(tx_df['from_address'].dropna())
            to_addresses = set(tx_df['to_address'].dropna())
            all_addresses = list(from_addresses.union(to_addresses))
            
            # Create address mapping
            self.index_to_address = {i: addr for i, addr in enumerate(all_addresses)}
            address_to_index = {addr: i for i, addr in enumerate(all_addresses)}
            
            # Build edges
            edge_list = []
            edge_weights = []
            
            for _, row in tx_df.iterrows():
                from_addr = row['from_address']
                to_addr = row['to_address']
                
                if pd.isna(from_addr) or pd.isna(to_addr):
                    continue
                    
                from_idx = address_to_index.get(from_addr)
                to_idx = address_to_index.get(to_addr)
                
                if from_idx is not None and to_idx is not None:
                    edge_list.append([from_idx, to_idx])
                    edge_weights.append(float(row.get('value_eth', 0.0)))
            
            # Create tensors
            if edge_list:
                edge_index = torch.tensor(edge_list, dtype=torch.long).t().contiguous()
                edge_attr = torch.tensor(edge_weights, dtype=torch.float).unsqueeze(1)
            else:
                edge_index = torch.empty((2, 0), dtype=torch.long)
                edge_attr = torch.empty((0, 1), dtype=torch.float)
            
            # Create node features (simplified)
            num_nodes = len(all_addresses)
            node_features = torch.randn(num_nodes, 8)  # 8-dimensional features
            
            # Create mock graph data object
            graph_data = type('GraphData', (), {
                'edge_index': edge_index,
                'edge_attr': edge_attr,
                'x': node_features,
                'num_nodes': num_nodes
            })()
            
            return GraphSnapshot(
                graph_data=graph_data,
                window_start=window_start,
                window_end=window_end,
                construction_time_ms=50,
                num_nodes=num_nodes,
                num_edges=len(edge_list)
            )
            
        except Exception as e:
            self.logger.error(f"Error building graph: {str(e)}")
            return self.create_empty_graph()
    
    def create_empty_graph(self):
        """Create empty graph snapshot"""
        import torch
        
        empty_graph_data = type('GraphData', (), {
            'edge_index': torch.empty((2, 0), dtype=torch.long),
            'edge_attr': torch.empty((0, 1), dtype=torch.float),
            'x': torch.empty((0, 8), dtype=torch.float),
            'num_nodes': 0
        })()
        
        return GraphSnapshot(
            graph_data=empty_graph_data,
            window_start=datetime.now(),
            window_end=datetime.now(),
            construction_time_ms=0,
            num_nodes=0,
            num_edges=0
        )
    
    def serialize_graph_snapshot(self, snapshot):
        """Serialize graph snapshot to JSON"""
        try:
            data = {
                'num_nodes': snapshot.num_nodes,
                'num_edges': snapshot.num_edges,
                'window_start': snapshot.window_start.isoformat() if hasattr(snapshot.window_start, 'isoformat') else str(snapshot.window_start),
                'window_end': snapshot.window_end.isoformat() if hasattr(snapshot.window_end, 'isoformat') else str(snapshot.window_end),
                'construction_time_ms': snapshot.construction_time_ms,
                'processing_stats': snapshot.processing_stats
            }
            return json.dumps(data)
        except Exception as e:
            self.logger.error(f"Error serializing graph: {str(e)}")
            return json.dumps({'error': 'serialization_failed'})


class SparkGraphBuilder:
    """Fixed Spark Graph Builder with resolved column reference issues"""
    
    def __init__(self):
        """Initialize with fixed schema handling"""
        self.settings = GraphLayerSettings()
        self.logger = logging.getLogger(__name__)
        self.spark = None
        self.graph_constructor = None
        
        # Initialize components
        self._init_spark_session()
        self._init_graph_constructor()
        
    def _init_spark_session(self):
        """Initialize optimized Spark session"""
        self.logger.info("🚀 Initializing Spark session with column reference fixes")
        
        try:
            # Check for local JARs first, then fall back to packages
            spark_jars_dir = os.getenv("SPARK_JARS_DIR", "/tmp")
            neo4j_jar = "/tmp/neo4j-connector-apache-spark_2.12-5.0.1.jar"
            if os.path.exists(neo4j_jar):
                # Use pre-downloaded Neo4j JAR
                self.logger.info(f"📦 Using local Neo4j JAR: {neo4j_jar}")
                
                self.spark = SparkSession.builder \
                    .appName("Fixed-AML-Graph-Construction") \
                    .master("local[*]") \
                    .config("spark.jars", neo4j_jar) \
                    .config("spark.jars.packages", "org.apache.spark:spark-sql-kafka-0-10_2.12:3.5.0") \
                    .config("spark.executor.memory", self.settings.spark_executor_memory) \
                    .config("spark.driver.memory", self.settings.spark_driver_memory) \
                    .config("spark.sql.adaptive.enabled", "true") \
                    .config("spark.sql.adaptive.coalescePartitions.enabled", "true") \
                    .config("spark.streaming.stopGracefullyOnShutdown", "true") \
                    .config("spark.sql.streaming.forceDeleteTempCheckpointLocation", "true") \
                    .config("spark.sql.streaming.stateStore.rocksdb.trackTotalNumberOfRows", "false") \
                    .config("spark.sql.streaming.stateStore.rocksdb.changelogCheckpointing.enabled", "true") \
                    .config("spark.sql.streaming.stateStore.rocksdb.boundedMemoryUsage", "true") \
                    .config("spark.sql.streaming.stateStore.rocksdb.maxMemoryUsageMB", "256") \
                    .config("spark.sql.shuffle.partitions", "4") \
                    .getOrCreate()
            else:
                # Fall back to downloading packages (original method)
                self.logger.info("📦 Downloading JARs from Maven repositories...")
                self.spark = SparkSession.builder \
                    .appName("Fixed-AML-Graph-Construction") \
                    .master("local[*]") \
                    .config("spark.jars.packages", 
                            "org.neo4j:neo4j-connector-apache-spark_2.12:5.0.1,"
                            "org.apache.spark:spark-sql-kafka-0-10_2.12:3.5.0") \
                    .config("spark.executor.memory", self.settings.spark_executor_memory) \
                    .config("spark.driver.memory", self.settings.spark_driver_memory) \
                    .config("spark.sql.adaptive.enabled", "true") \
                    .config("spark.sql.adaptive.coalescePartitions.enabled", "true") \
                    .config("spark.streaming.stopGracefullyOnShutdown", "true") \
                    .config("spark.sql.streaming.forceDeleteTempCheckpointLocation", "true") \
                    .config("spark.sql.streaming.stateStore.rocksdb.trackTotalNumberOfRows", "false") \
                    .config("spark.sql.streaming.stateStore.rocksdb.changelogCheckpointing.enabled", "true") \
                    .config("spark.sql.streaming.stateStore.rocksdb.boundedMemoryUsage", "true") \
                    .config("spark.sql.streaming.stateStore.rocksdb.maxMemoryUsageMB", "256") \
                    .config("spark.sql.shuffle.partitions", "4") \
                    .getOrCreate()
            
            self.spark.sparkContext.setLogLevel("WARN")
            self.logger.info("✅ Spark session initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Spark session: {e}")
            raise
    
    def _init_graph_constructor(self):
        """Initialize graph constructor"""
        self.graph_constructor = Context7GraphConstructor(self.settings)
        self.logger.info("✅ Graph constructor initialized")
    
    def _get_transaction_schema(self) -> StructType:
        """Define schema for incoming transaction data from CEP"""
        
        # Direct transaction schema matching CEP output
        return StructType([
            StructField("txId", StringType(), True),
            StructField("fromAddr", StringType(), True),
            StructField("toAddr", StringType(), True),
            StructField("valueEth", DoubleType(), True),
            StructField("blockNumber", LongType(), True),
            StructField("timestamp", LongType(), True),
            StructField("gasPrice", LongType(), True),
            StructField("gasUsed", LongType(), True)
        ])
    
    def _create_fixed_kafka_source(self) -> DataFrame:
        """Create Kafka source with FIXED column referencing"""
        
        self.logger.info(f"🔄 Creating FIXED Kafka source for topic: {self.settings.kafka_input_topic}")
        
        # Kafka connection options
        kafka_options = {
            "kafka.bootstrap.servers": self.settings.kafka_bootstrap_servers,
            "subscribe": self.settings.kafka_input_topic,
            "startingOffsets": "earliest",
            "maxOffsetsPerTrigger": "100",
            "failOnDataLoss": "false",
            "kafka.group.id": self.settings.kafka_consumer_group
        }
        
        # Create Kafka source
        kafka_df = self.spark \
            .readStream \
            .format("kafka") \
            .options(**kafka_options) \
            .load()
        
        # Parse with transaction schema (direct from CEP)
        transaction_schema = self._get_transaction_schema()
        
        # Step 1: Parse JSON and flatten the structure correctly
        parsed_df = kafka_df.select(
            col("key").cast("string"),
            from_json(col("value").cast("string"), transaction_schema).alias("tx"),
            col("timestamp").alias("kafka_timestamp"),
            col("partition"),
            col("offset")
        ).select(
            "key",
            "tx.txId",        # Correctly reference nested fields
            "tx.fromAddr", 
            "tx.toAddr",
            "tx.valueEth",
            "tx.blockNumber",
            "tx.timestamp",
            "tx.gasPrice",
            "tx.gasUsed",
            "kafka_timestamp",
            "partition",
            "offset"
        ).filter(
            col("fromAddr").isNotNull() &
            col("toAddr").isNotNull() &
            col("valueEth").isNotNull()
        )
        
        # Step 2: Map fields for graph construction with correct column references
        transactions_df = parsed_df.select(
            # Use the direct column names now, not nested references
            col("txId").alias("tx_id"),
            col("fromAddr").alias("from_address"),
            col("toAddr").alias("to_address"),
            col("valueEth").alias("value_eth"),
            col("timestamp").alias("tx_timestamp"),
            col("blockNumber").alias("block_number"),
            col("gasUsed").alias("gas_used"),
            col("gasPrice").alias("gas_price"),
            col("kafka_timestamp"),
            col("partition"),
            col("offset")
        ).filter(
            # Filter valid transactions
            (col("from_address").isNotNull()) &
            (col("to_address").isNotNull()) &
            (col("from_address") != col("to_address")) &
            (col("value_eth") >= 0)
        )
        
        # Log schema for debugging
        self.logger.info(f"🔍 DEBUG: Transaction DataFrame schema: {transactions_df.schema}")
        
        self.logger.info("✅ FIXED Transaction DataFrame created with resolved column references")
        return transactions_df
    
    def _apply_fixed_windowing(self, df: DataFrame) -> DataFrame:
        """Apply windowing with FIXED timestamp handling"""
        
        # Normalize timestamps
        current_time = int(time.time() * 1000)
        max_future_time = current_time + (5 * 60 * 1000)
        min_valid_time = current_time - (365 * 24 * 60 * 60 * 1000)
        
        self.logger.info(f"🔧 Applying FIXED timestamp normalization. Current: {current_time}")
        
        # Normalize and convert timestamps
        # Note: tx_timestamp is in seconds, not milliseconds
        normalized_df = df.withColumn(
            "normalized_timestamp",
            when(
                (col("tx_timestamp") > (max_future_time / 1000)) | (col("tx_timestamp") < (min_valid_time / 1000)),
                lit(current_time / 1000)
            ).otherwise(col("tx_timestamp"))
        ).withColumn(
            "event_time",
            to_timestamp(from_unixtime(col("normalized_timestamp").cast("double")))
        )
        
        # Apply watermark and windowing
        windowed_df = normalized_df \
            .withWatermark("event_time", self.settings.watermark_delay) \
            .groupBy(
                window("event_time", 
                       self.settings.window_duration,
                       self.settings.sliding_duration)
            ) \
            .agg(
                count("*").alias("transaction_count"),
                collect_list(struct(
                    "tx_id", "from_address", "to_address", 
                    "value_eth", "tx_timestamp", "event_time"
                )).alias("transactions"),
                first("kafka_timestamp").alias("first_detection_time")
            ) \
            .filter(col("transaction_count") >= 1) \
            .select(
            col("window.start").alias("window_start"),
            col("window.end").alias("window_end"),
            "transaction_count",
            "transactions",
                "first_detection_time"
        )
        
        self.logger.info("✅ FIXED windowing applied successfully")
        return windowed_df
    
    def _process_fixed_window_batch(self, batch_df: DataFrame, batch_id: int):
        """Process batch with FIXED error handling and logging"""
    
        batch_start_time = time.time()
    
        try:
            self.logger.info(f"🔄 Processing FIXED batch {batch_id}")
            
            # Debug: Check if we're getting any raw transaction data first
            try:
                raw_tx_count = batch_df.count() if batch_df else 0
                self.logger.info(f"🔍 DEBUG: Raw batch DataFrame count: {raw_tx_count}")
                
                if raw_tx_count > 0:
                    # Show schema to understand what we're receiving
                    self.logger.info(f"🔍 DEBUG: Batch DataFrame schema: {batch_df.schema}")
                    sample_rows = batch_df.limit(2).collect()
                    for i, row in enumerate(sample_rows):
                        self.logger.info(f"🔍 DEBUG Sample row {i}: {row}")
                        
            except Exception as debug_e:
                self.logger.info(f"🔍 DEBUG Error: {debug_e}")
            
            row_count = batch_df.count()
            self.logger.info(f"   Batch {batch_id}: {row_count} windows")
        
            if row_count > 0:
                # Process each window
                windows_processed = 0
                for row in batch_df.collect():
                    try:
                        window_start = row.window_start
                        window_end = row.window_end
                        transactions = row.transactions if hasattr(row, 'transactions') else []
                
                        self.logger.info(f"   Window: {window_start} → {window_end}, {len(transactions)} txs")
                
                        if transactions:
                            # Convert to pandas DataFrame with FIXED field mapping
                            transactions_data = []
                            for tx in transactions:
                                transactions_data.append({
                                    'tx_id': getattr(tx, 'tx_id', 'unknown'),
                                    'from_address': getattr(tx, 'from_address', 'unknown'),
                                    'to_address': getattr(tx, 'to_address', 'unknown'),
                                    'value_eth': getattr(tx, 'value_eth', 0.0),
                                    'timestamp': getattr(tx, 'tx_timestamp', 0),
                                    'isContract': getattr(tx, 'isContract', False)
                                })
                    
                        # Build graph
                        tx_df = pd.DataFrame(transactions_data)
                        graph_snapshot = self.graph_constructor.build_graph_from_transactions(
                            tx_df, window_start, window_end
                        )
                    
                            # Output and export
                        self._output_graph_snapshot(graph_snapshot, batch_id)
                        self._export_to_neo4j_safe(graph_snapshot, batch_id)  # Re-enabled with fixed JAR
                    
                        windows_processed += 1
                        self.logger.info(f"   ✅ Graph: {graph_snapshot.num_nodes} nodes, {graph_snapshot.num_edges} edges")
                    
                    except Exception as e:
                        self.logger.error(f"   ❌ Error processing window in batch {batch_id}: {str(e)}")
                
                self.logger.info(f"✅ Batch {batch_id}: processed {windows_processed}/{row_count} windows")
            else:
                # Handle empty batch
                empty_snapshot = self.graph_constructor.create_empty_graph()
                self._output_graph_snapshot(empty_snapshot, batch_id)
                self.logger.info(f"✅ Batch {batch_id}: empty, created placeholder graph")
            
            batch_time = (time.time() - batch_start_time) * 1000
            self.logger.info(f"✅ Batch {batch_id} completed in {batch_time:.2f}ms")
            
        except Exception as e:
            self.logger.error(f"❌ CRITICAL: Batch {batch_id} failed: {str(e)}", exc_info=True)
    
    def _export_to_neo4j_safe(self, snapshot: GraphSnapshot, batch_id: int):
        """Safe Neo4j export using Python driver (Context7 best practice)"""
        try:
            if snapshot.num_nodes == 0:
                self.logger.info(f"   Skipping Neo4j export for empty graph (batch {batch_id})")
                return
            
            try:
                from neo4j import GraphDatabase
            except ImportError:
                self.logger.warning("   ⚠️ Neo4j Python driver not available, skipping Neo4j export")
                return
            
            # Create node data
            nodes_data = []
            for idx in range(snapshot.num_nodes):
                if idx in self.graph_constructor.index_to_address:
                    address = self.graph_constructor.index_to_address[idx]
                    nodes_data.append({
                        'address': address,
                        'batch_id': batch_id,
                        'window_start': snapshot.window_start.isoformat() if hasattr(snapshot.window_start, 'isoformat') else str(snapshot.window_start),
                        'window_end': snapshot.window_end.isoformat() if hasattr(snapshot.window_end, 'isoformat') else str(snapshot.window_end)
                    })
            
            # Export using Neo4j Python driver with optimized batch operations
            if nodes_data:
                try:
                    with GraphDatabase.driver(
                        self.settings.neo4j_uri.replace("bolt://", "neo4j://"),
                        auth=(self.settings.neo4j_user, self.settings.neo4j_password),
                        max_connection_pool_size=10,
                        connection_timeout=30,
                        max_retry_time=30
                    ) as driver:
                        with driver.session() as session:
                            # Batch insert for better performance
                            with session.begin_transaction() as tx:
                                # Create nodes in batch
                                tx.run(
                                    """
                                    UNWIND $nodes AS node
                                    MERGE (a:Address {address: node.address, batch_id: node.batch_id})
                                    SET a.window_start = node.window_start,
                                        a.window_end = node.window_end,
                                        a.last_updated = datetime(),
                                        a.node_count = 1
                                    """,
                                    nodes=nodes_data
                                )
                                
                                # Create edges if available from graph constructor
                                if hasattr(self.graph_constructor, 'current_edges') and self.graph_constructor.current_edges:
                                    edges_data = []
                                    for edge in self.graph_constructor.current_edges:
                                        if hasattr(edge, 'source') and hasattr(edge, 'target'):
                                            edges_data.append({
                                                'from_addr': edge.source,
                                                'to_addr': edge.target,
                                                'weight': getattr(edge, 'weight', 1),
                                                'batch_id': batch_id
                                            })
                                    
                                    if edges_data:
                                        tx.run(
                                            """
                                            UNWIND $edges AS edge
                                            MATCH (from:Address {address: edge.from_addr})
                                            MATCH (to:Address {address: edge.to_addr})
                                            MERGE (from)-[r:TRANSACTS_WITH {batch_id: edge.batch_id}]->(to)
                                            SET r.weight = edge.weight,
                                                r.last_updated = datetime()
                                            """,
                                            edges=edges_data
                                        )
                                        self.logger.info(f"   ✅ Neo4j: exported {len(edges_data)} edges")
                
                except Exception as neo4j_error:
                    self.logger.error(f"   ❌ Neo4j connection failed: {str(neo4j_error)}")
                    # Fallback: continue without Neo4j export
                    return
        
                self.logger.info(f"   ✅ Neo4j: exported {len(nodes_data)} nodes using Python driver")
        
        except Exception as e:
            self.logger.error(f"   ❌ Neo4j export failed for batch {batch_id}: {str(e)}")
    
    def _output_graph_snapshot(self, snapshot: GraphSnapshot, batch_id: int):
        """Output graph snapshot to Kafka and file"""
        self.logger.info(f"   🔄 Starting graph snapshot output for batch {batch_id}")
        
        try:
            # Create simplified output message without serialization
            output_message = {
                "batch_id": batch_id,
                "num_nodes": snapshot.num_nodes,
                "num_edges": snapshot.num_edges,
                "processing_timestamp": datetime.now().isoformat(),
                "window_start": str(snapshot.window_start) if hasattr(snapshot, 'window_start') else None,
                "window_end": str(snapshot.window_end) if hasattr(snapshot, 'window_end') else None
            }
            
            self.logger.info(f"   📝 Created output message: {output_message}")
            
            # Send to Kafka topic
            try:
                self._send_to_kafka(output_message)
            except Exception as kafka_error:
                self.logger.error(f"   ❌ Kafka send error: {str(kafka_error)}")
            
            # Log output
            self.logger.info(f"   📊 Graph snapshot ready: {snapshot.num_nodes} nodes, {snapshot.num_edges} edges")
                
        except Exception as e:
            self.logger.error(f"   ❌ Error outputting snapshot for batch {batch_id}: {str(e)}", exc_info=True)
    
    def _send_to_kafka(self, message: dict):
        """Send message to Kafka graph-snapshots topic"""
        self.logger.info(f"   🔄 Attempting to send to Kafka: {self.settings.kafka_output_topic}")
        
        try:
            from kafka import KafkaProducer
            
            self.logger.info(f"   📡 Creating Kafka producer for: {self.settings.kafka_bootstrap_servers}")
            
            producer = KafkaProducer(
                bootstrap_servers=self.settings.kafka_bootstrap_servers,
                value_serializer=lambda x: json.dumps(x).encode('utf-8'),
                retries=3,
                request_timeout_ms=30000
            )
            
            self.logger.info(f"   📤 Sending message to topic: {self.settings.kafka_output_topic}")
            future = producer.send(self.settings.kafka_output_topic, message)
            producer.flush()
            
            # Wait for send to complete
            record_metadata = future.get(timeout=10)
            producer.close()
            
            self.logger.info(f"   ✅ Sent graph snapshot to Kafka topic: {self.settings.kafka_output_topic}, partition: {record_metadata.partition}, offset: {record_metadata.offset}")
            
        except ImportError as ie:
            self.logger.error(f"   ❌ Kafka import error: {str(ie)}")
        except Exception as e:
            self.logger.error(f"   ❌ Failed to send to Kafka: {str(e)}", exc_info=True)
    
    def run_fixed_streaming_job(self) -> Optional[StreamingQuery]:
        """Run streaming job with ALL FIXES applied"""
        
        self.logger.info("🚀 Starting FIXED streaming job with column reference resolution")
        
        try:
            # Create fixed source
            source_df = self._create_fixed_kafka_source()
            
            # Apply fixed windowing
            windowed_df = self._apply_fixed_windowing(source_df)
            
            # Ensure checkpoint directory exists
            os.makedirs(self.settings.checkpoint_location, exist_ok=True)
            
            # Start streaming with fixed processing
            self.logger.info(f"🔄 Starting query with trigger: {self.settings.trigger_processing_time}")
            
            query = windowed_df.writeStream \
                .trigger(processingTime=self.settings.trigger_processing_time) \
                .option("truncate", "false") \
                .option("checkpointLocation", self.settings.checkpoint_location) \
                .foreachBatch(self._process_fixed_window_batch) \
                .start()
            
            self.logger.info(f"✅ FIXED streaming query started!")
            self.logger.info(f"   Query ID: {query.id}")
            self.logger.info(f"   Input topic: {self.settings.kafka_input_topic}")
            self.logger.info(f"   Consumer group: {self.settings.kafka_consumer_group}")
            self.logger.info(f"   Checkpoint: {self.settings.checkpoint_location}")
            
            return query
            
        except Exception as e:
            self.logger.error(f"❌ CRITICAL: Failed to start streaming job: {str(e)}", exc_info=True)
            raise
    
    def stop(self):
        """Stop streaming application"""
        self.logger.info("🛑 Stopping Graph Construction Layer")
        
        if self.spark:
            for stream in self.spark.streams.active:
                stream.stop()
            self.spark.stop()
            
        self.logger.info("✅ Graph Construction Layer stopped")


def main():
    """Main entry point with comprehensive error handling"""
    
    logger.info("🚀 Starting FIXED Graph Construction Layer")
    
    builder = SparkGraphBuilder()
    
    try:
        query = builder.run_fixed_streaming_job()
        
        if query:
            logger.info("✅ Streaming job running, waiting for termination...")
            query.awaitTermination()
        else:
            logger.error("❌ Failed to start streaming query")
            
    except KeyboardInterrupt:
        logger.info("🛑 Received interrupt signal, stopping gracefully...")
    except Exception as e:
        logger.error(f"❌ CRITICAL APPLICATION ERROR: {str(e)}", exc_info=True)
    finally:
        builder.stop()
        logger.info("🏁 Application shutdown complete")


if __name__ == "__main__":
    main() 