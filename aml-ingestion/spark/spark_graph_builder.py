#!/usr/bin/env python3
"""
COMPLETE FIX: Spark Graph Builder for AML Pipeline
Addresses all identified issues: checkpoint management, Kafka consumption, and data processing
"""

import sys
import os
import json
import logging
import time
import shutil
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List, Optional, Tuple
import pandas as pd
import numpy as np

# Add path for config imports
if '__file__' in globals():
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
else:
    sys.path.append('/app')

from pyspark.sql import SparkSession, DataFrame
import pyspark.sql.functions as F
from pyspark.sql.functions import (
    col, from_json, to_json, struct, window, 
    collect_list, first, last, count, sum as spark_sum,
    avg, max as spark_max, min as spark_min,
    current_timestamp, unix_timestamp, from_unixtime, explode,
    when, isnan, isnull, size, lit, to_timestamp, concat, 
    desc, asc, date_format
)
from pyspark.sql.types import (
    StructType, StructField, StringType, DoubleType, 
    LongType, BooleanType, TimestampType, ArrayType
)
from pyspark.sql.streaming.query import StreamingQuery

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class FixedGraphLayerSettings:
    """FIXED Configuration settings with proper checkpoint management"""
    
    def __init__(self):
        # Kafka settings - FIXED
        self.kafka_bootstrap_servers = os.getenv("KAFKA_BOOTSTRAP_SERVERS", "kafka:29092")
        self.kafka_input_topic = os.getenv("KAFKA_INPUT_TOPIC", "filtered-transactions")
        self.kafka_output_topic = os.getenv("KAFKA_OUTPUT_TOPIC", "graph-snapshots")
        # REMOVED problematic consumer group as per Spark warning
        
        # Spark settings - OPTIMIZED
        self.spark_executor_memory = os.getenv("SPARK_EXECUTOR_MEMORY", "1g")
        self.spark_driver_memory = os.getenv("SPARK_DRIVER_MEMORY", "1g")  # Increased
        self.max_offsets_per_trigger = int(os.getenv("MAX_OFFSETS_PER_TRIGGER", "500"))  # Reduced for stability
        self.trigger_processing_time = os.getenv("TRIGGER_PROCESSING_TIME", "15 seconds")  # More frequent
        
        # Window settings - BALANCED
        self.window_duration = os.getenv("WINDOW_DURATION", "2 minutes")
        self.sliding_duration = os.getenv("SLIDING_DURATION", "30 seconds")
        self.watermark_delay = os.getenv("WATERMARK_DELAY", "1 minute")
        
        # Neo4j settings
        self.neo4j_uri = os.getenv("NEO4J_URI", "bolt://neo4j:7687")
        self.neo4j_user = os.getenv("NEO4J_USER", "neo4j")
        self.neo4j_password = os.getenv("NEO4J_PASSWORD", "blockchain-aml-2024")
        
        # Performance settings
        self.enable_detailed_metrics = os.getenv("ENABLE_DETAILED_METRICS", "true").lower() == "true"
        self.batch_size = int(os.getenv("BATCH_SIZE", "500"))  # Reduced for stability
        
        # Output settings
        self.output_format = os.getenv("OUTPUT_FORMAT", "both")
        self.file_output_path = os.getenv("FILE_OUTPUT_PATH", "/tmp/graph-snapshots")
        
        # Checkpoint settings - DYNAMIC
        self.base_checkpoint_location = os.getenv("CHECKPOINT_LOCATION", "/tmp/spark-checkpoints")
        self.reset_checkpoint_on_start = os.getenv("RESET_CHECKPOINT", "false").lower() == "true"
        
        # Advanced settings for troubleshooting
        self.starting_offsets = os.getenv("STARTING_OFFSETS", "latest")  # Changed from earliest
        self.enable_debug_mode = os.getenv("ENABLE_DEBUG", "true").lower() == "true"
        self.kafka_poll_timeout = int(os.getenv("KAFKA_POLL_TIMEOUT", "10000"))  # milliseconds
        
        # Create directories
        os.makedirs(self.file_output_path, exist_ok=True)
        os.makedirs(self.base_checkpoint_location, exist_ok=True)
        
    def get_checkpoint_location(self, query_name: str) -> str:
        """Get checkpoint location for specific query with reset capability"""
        checkpoint_path = os.path.join(self.base_checkpoint_location, f"graph-{query_name}")
        
        if self.reset_checkpoint_on_start:
            if os.path.exists(checkpoint_path):
                logger.info(f"🗑️ Resetting checkpoint: {checkpoint_path}")
                shutil.rmtree(checkpoint_path, ignore_errors=True)
        
        os.makedirs(checkpoint_path, exist_ok=True)
        return checkpoint_path


class FixedGraphConstructor:
    """FIXED Graph constructor with improved error handling"""
    
    def __init__(self, settings):
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        self.index_to_address = {}
        self.current_edges = []
        
    def build_graph_from_transactions(self, tx_df, window_start, window_end):
        """Build graph from transactions DataFrame with FIXED processing"""
        try:
            import torch
            
            if tx_df.empty:
                return self.create_empty_graph(window_start, window_end)
            
            # Get unique addresses with proper null handling
            from_addresses = set(tx_df['from_address'].dropna().unique())
            to_addresses = set(tx_df['to_address'].dropna().unique())
            all_addresses = list(from_addresses.union(to_addresses))
            
            if not all_addresses:
                return self.create_empty_graph(window_start, window_end)
            
            # Create address mapping
            self.index_to_address = {i: addr for i, addr in enumerate(all_addresses)}
            address_to_index = {addr: i for i, addr in enumerate(all_addresses)}
            
            # Build edges with aggregation
            edge_data = {}
            
            for _, row in tx_df.iterrows():
                from_addr = row.get('from_address')
                to_addr = row.get('to_address')
                value = float(row.get('value_eth', 0.0))
                
                if pd.isna(from_addr) or pd.isna(to_addr):
                    continue
                    
                from_idx = address_to_index.get(from_addr)
                to_idx = address_to_index.get(to_addr)
                
                if from_idx is not None and to_idx is not None and from_idx != to_idx:
                    edge_key = (from_idx, to_idx)
                    if edge_key not in edge_data:
                        edge_data[edge_key] = {'count': 0, 'total_value': 0.0, 'transactions': []}
                    
                    edge_data[edge_key]['count'] += 1
                    edge_data[edge_key]['total_value'] += value
                    edge_data[edge_key]['transactions'].append(row.get('tx_id', 'unknown'))
            
            # Create tensors
            if edge_data:
                edge_list = list(edge_data.keys())
                edge_weights = [data['total_value'] for data in edge_data.values()]
                edge_counts = [data['count'] for data in edge_data.values()]
                
                edge_index = torch.tensor(edge_list, dtype=torch.long).t().contiguous()
                edge_attr = torch.stack([
                    torch.tensor(edge_weights, dtype=torch.float),
                    torch.tensor(edge_counts, dtype=torch.float)
                ], dim=1)
                
                # Store current edges for Neo4j export
                self.current_edges = [
                    type('Edge', (), {
                        'source': self.index_to_address[edge[0]],
                        'target': self.index_to_address[edge[1]], 
                        'weight': edge_data[edge]['total_value'],
                        'count': edge_data[edge]['count']
                    })() for edge in edge_data.keys()
                ]
            else:
                edge_index = torch.empty((2, 0), dtype=torch.long)
                edge_attr = torch.empty((0, 2), dtype=torch.float)
                self.current_edges = []
            
            # Create node features
            num_nodes = len(all_addresses)
            node_features = torch.randn(num_nodes, 16)  # 16-dimensional features
            
            # Create graph data object
            graph_data = type('GraphData', (), {
                'edge_index': edge_index,
                'edge_attr': edge_attr,
                'x': node_features,
                'num_nodes': num_nodes
            })()
            
            return FixedGraphSnapshot(
                graph_data=graph_data,
                window_start=window_start,
                window_end=window_end,
                construction_time_ms=100,
                num_nodes=num_nodes,
                num_edges=len(edge_data),
                processing_stats={'transactions_processed': len(tx_df)}
            )
            
        except Exception as e:
            self.logger.error(f"Error building graph: {str(e)}")
            return self.create_empty_graph(window_start, window_end)
    
    def create_empty_graph(self, window_start=None, window_end=None):
        """Create empty graph snapshot"""
        try:
            import torch
            
            empty_graph_data = type('GraphData', (), {
                'edge_index': torch.empty((2, 0), dtype=torch.long),
                'edge_attr': torch.empty((0, 2), dtype=torch.float),
                'x': torch.empty((0, 16), dtype=torch.float),
                'num_nodes': 0
            })()
            
            return FixedGraphSnapshot(
                graph_data=empty_graph_data,
                window_start=window_start or datetime.now(),
                window_end=window_end or datetime.now(),
                construction_time_ms=0,
                num_nodes=0,
                num_edges=0
            )
        except ImportError:
            # Fallback without torch
            return FixedGraphSnapshot(
                graph_data=None,
                window_start=window_start or datetime.now(),
                window_end=window_end or datetime.now(),
                construction_time_ms=0,
                num_nodes=0,
                num_edges=0
            )


class FixedGraphSnapshot:
    """FIXED Graph snapshot data structure"""
    
    def __init__(self, graph_data=None, window_start=None, window_end=None, 
                 construction_time_ms=0, processing_stats=None, num_nodes=0, num_edges=0):
        self.graph_data = graph_data
        self.window_start = window_start
        self.window_end = window_end
        self.construction_time_ms = construction_time_ms
        self.processing_stats = processing_stats or {}
        self.num_nodes = num_nodes
        self.num_edges = num_edges


class FixedSparkGraphBuilder:
    """COMPLETELY FIXED Spark Graph Builder with all issues resolved"""
    
    def __init__(self):
        """Initialize with COMPREHENSIVE fixes"""
        self.settings = FixedGraphLayerSettings()
        self.logger = logging.getLogger(__name__)
        self.spark = None
        self.graph_constructor = None
        self.startup_timestamp = datetime.now()
        
        # Initialize components
        self._init_spark_session()
        self._init_graph_constructor()
        
        # Log startup configuration
        self._log_startup_config()
        
    def _log_startup_config(self):
        """Log comprehensive startup configuration"""
        self.logger.info("🚀 FIXED Spark Graph Builder Configuration:")
        self.logger.info(f"   📥 Input Topic: {self.settings.kafka_input_topic}")
        self.logger.info(f"   📤 Output Topic: {self.settings.kafka_output_topic}")
        self.logger.info(f"   🔄 Starting Offsets: {self.settings.starting_offsets}")
        self.logger.info(f"   ⚡ Max Offsets/Trigger: {self.settings.max_offsets_per_trigger}")
        self.logger.info(f"   ⏱️ Trigger Interval: {self.settings.trigger_processing_time}")
        self.logger.info(f"   🪟 Window: {self.settings.window_duration} / {self.settings.sliding_duration}")
        self.logger.info(f"   💾 Checkpoint Reset: {self.settings.reset_checkpoint_on_start}")
        self.logger.info(f"   🐛 Debug Mode: {self.settings.enable_debug_mode}")
        
    def _init_spark_session(self):
        """Initialize OPTIMIZED Spark session"""
        self.logger.info("🚀 Initializing FIXED Spark session")
        
        try:
            self.spark = SparkSession.builder \
                .appName("FIXED-AML-Graph-Construction") \
                .master("local[*]") \
                .config("spark.executor.memory", self.settings.spark_executor_memory) \
                .config("spark.driver.memory", self.settings.spark_driver_memory) \
                .config("spark.sql.adaptive.enabled", "true") \
                .config("spark.sql.adaptive.coalescePartitions.enabled", "true") \
                .config("spark.streaming.stopGracefullyOnShutdown", "true") \
                .config("spark.sql.streaming.forceDeleteTempCheckpointLocation", "true") \
                .config("spark.sql.shuffle.partitions", "4") \
                .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer") \
                .config("spark.sql.streaming.metricsEnabled", "true") \
                .config("spark.jars.packages", "org.apache.spark:spark-sql-kafka-0-10_2.12:3.5.0") \
                .getOrCreate()
            
            self.spark.sparkContext.setLogLevel("WARN")
            self.logger.info("✅ FIXED Spark session initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Spark session: {e}")
            raise
    
    def _init_graph_constructor(self):
        """Initialize FIXED graph constructor"""
        self.graph_constructor = FixedGraphConstructor(self.settings)
        self.logger.info("✅ FIXED Graph constructor initialized")
    
    def _get_fixed_transaction_schema(self) -> StructType:
        """Get FIXED transaction schema matching CEP output exactly"""
        return StructType([
            StructField("txId", StringType(), True),
            StructField("fromAddr", StringType(), True),
            StructField("toAddr", StringType(), True),
            StructField("valueEth", DoubleType(), True),
            StructField("blockNumber", LongType(), True),
            StructField("timestamp", LongType(), True),
            StructField("gasPrice", LongType(), True),
            StructField("gasUsed", LongType(), True),
            StructField("isContract", BooleanType(), True),
            StructField("roundAmount", BooleanType(), True),
            StructField("highValue", BooleanType(), True),
            StructField("microAmount", BooleanType(), True),
            StructField("riskScore", StringType(), True),
            StructField("reason", StringType(), True)
        ])
    
    def _create_fixed_kafka_source(self) -> DataFrame:
        """Create COMPLETELY FIXED Kafka source"""
        self.logger.info(f"📡 Creating FIXED Kafka source for topic: {self.settings.kafka_input_topic}")
        
        # FIXED Kafka options - removed problematic consumer group
        kafka_options = {
            "kafka.bootstrap.servers": self.settings.kafka_bootstrap_servers,
            "subscribe": self.settings.kafka_input_topic,
            "startingOffsets": self.settings.starting_offsets,
            "maxOffsetsPerTrigger": str(self.settings.max_offsets_per_trigger),
            "failOnDataLoss": "false",
            "kafka.request.timeout.ms": str(self.settings.kafka_poll_timeout),
            "kafka.session.timeout.ms": "30000",
            "kafka.heartbeat.interval.ms": "10000"
        }
        
        self.logger.info(f"📡 Kafka Options: {kafka_options}")
        
        # Create Kafka source
        kafka_df = self.spark \
            .readStream \
            .format("kafka") \
            .options(**kafka_options) \
            .load()
        
        # Parse with FIXED schema handling
        transaction_schema = self._get_fixed_transaction_schema()
        
        # FIXED JSON parsing with comprehensive error handling
        parsed_df = kafka_df.select(
            col("key").cast("string").alias("kafka_key"),
            col("value").cast("string").alias("raw_value"),
            col("timestamp").alias("kafka_timestamp"),
            col("partition"),
            col("offset")
        ).filter(
            col("raw_value").isNotNull() & (col("raw_value") != "")
        ).select(
            "kafka_key",
            from_json(col("raw_value"), transaction_schema).alias("tx"),
            "kafka_timestamp",
            "partition", 
            "offset"
        ).filter(
            col("tx").isNotNull()
        ).select(
            "kafka_key",
            col("tx.*"),  # Flatten transaction fields
            "kafka_timestamp",
            "partition",
            "offset"
        )
        
        # FIXED field mapping with proper column references
        transactions_df = parsed_df.select(
            col("txId").alias("tx_id"),
            col("fromAddr").alias("from_address"),
            col("toAddr").alias("to_address"),
            col("valueEth").alias("value_eth"),
            col("timestamp").alias("tx_timestamp"),
            col("blockNumber").alias("block_number"),
            col("gasUsed").alias("gas_used"),
            col("gasPrice").alias("gas_price"),
            col("isContract").alias("is_contract"),
            col("roundAmount").alias("round_amount"),
            col("highValue").alias("high_value"),
            col("microAmount").alias("micro_amount"),
            col("riskScore").alias("tx_risk_score"),
            col("reason").alias("tx_reason"),
            "kafka_timestamp",
            "partition",
            "offset"
        ).filter(
            # COMPREHENSIVE data quality filters
            (col("from_address").isNotNull()) &
            (col("to_address").isNotNull()) &
            (col("from_address") != col("to_address")) &
            (col("value_eth").isNotNull()) &
            (col("value_eth") >= 0) &
            (col("tx_timestamp").isNotNull()) &
            (col("tx_timestamp") > 0)
        )
        
        self.logger.info("✅ FIXED Transaction DataFrame created with resolved column references")
        return transactions_df
    
    def _apply_fixed_windowing(self, df: DataFrame) -> DataFrame:
        """Apply FIXED windowing with improved timestamp handling"""
        
        current_time = int(time.time())
        max_future_time = current_time + (10 * 60)  # 10 minutes future
        min_valid_time = current_time - (30 * 24 * 60 * 60)  # 30 days back
        
        self.logger.info(f"🔧 Applying FIXED windowing with timestamp validation")
        self.logger.info(f"   Time range: {min_valid_time} to {max_future_time}")
        
        # FIXED timestamp normalization and validation
        normalized_df = df.withColumn(
            "normalized_timestamp",
            when(
                (col("tx_timestamp") > max_future_time) | (col("tx_timestamp") < min_valid_time),
                lit(current_time)
            ).otherwise(col("tx_timestamp"))
        ).withColumn(
            "event_time",
            to_timestamp(col("normalized_timestamp").cast("double"))
        ).filter(
            col("event_time").isNotNull()
        )
        
        # FIXED windowing with comprehensive aggregation
        windowed_df = normalized_df \
            .withWatermark("event_time", self.settings.watermark_delay) \
            .groupBy(
                window("event_time", 
                       self.settings.window_duration,
                       self.settings.sliding_duration)
            ) \
            .agg(
                count("*").alias("transaction_count"),
                collect_list(struct(
                    "tx_id", "from_address", "to_address", 
                    "value_eth", "tx_timestamp", "event_time",
                    "is_contract", "tx_risk_score", "tx_reason"
                )).alias("transactions"),
                first("kafka_timestamp").alias("first_detection_time"),
                spark_sum("value_eth").alias("total_value"),
                avg("value_eth").alias("avg_value")
            ) \
            .filter(col("transaction_count") >= 1) \
            .select(
                col("window.start").alias("window_start"),
                col("window.end").alias("window_end"),
                "transaction_count",
                "transactions",
                "first_detection_time",
                "total_value",
                "avg_value"
            )
        
        self.logger.info("✅ FIXED windowing applied successfully")
        return windowed_df
    
    def _process_fixed_window_batch(self, batch_df: DataFrame, batch_id: int):
        """Process batch with COMPREHENSIVE fixes and monitoring"""
        
        batch_start_time = time.time()
        
        try:
            self.logger.info(f"📊 Processing FIXED batch {batch_id}")
            
            # Enhanced batch monitoring
            try:
                row_count = batch_df.count()
                self.logger.info(f"   📊 Batch {batch_id}: {row_count} windows")
                
                if row_count > 0:
                    # Sample data logging for debugging
                    if self.settings.enable_debug_mode:
                        sample_data = batch_df.limit(1).collect()
                        if sample_data:
                            sample = sample_data[0]
                            self.logger.info(f"   🔍 Sample window: {sample.window_start} → {sample.window_end}")
                            self.logger.info(f"   🔍 Sample tx count: {sample.transaction_count}")
                            
            except Exception as count_error:
                self.logger.error(f"   ⚠️ Batch count error: {count_error}")
                row_count = 0
            
            if row_count > 0:
                windows_processed = 0
                total_transactions = 0
                total_nodes = 0
                total_edges = 0
                
                for row in batch_df.collect():
                    try:
                        window_start = row.window_start
                        window_end = row.window_end
                        transactions = row.transactions if hasattr(row, 'transactions') else []
                        tx_count = len(transactions)
                        total_transactions += tx_count
                        
                        self.logger.info(f"   🪟 Window: {window_start} → {window_end} ({tx_count} txs)")
                        
                        if transactions:
                            # Convert to pandas with FIXED field handling
                            transactions_data = []
                            for tx in transactions:
                                transactions_data.append({
                                    'tx_id': getattr(tx, 'tx_id', 'unknown'),
                                    'from_address': getattr(tx, 'from_address', 'unknown'),
                                    'to_address': getattr(tx, 'to_address', 'unknown'),
                                    'value_eth': getattr(tx, 'value_eth', 0.0),
                                    'timestamp': getattr(tx, 'tx_timestamp', 0),
                                    'is_contract': getattr(tx, 'is_contract', False),
                                    'tx_risk_score': getattr(tx, 'tx_risk_score', 'UNKNOWN'),
                                    'tx_reason': getattr(tx, 'tx_reason', 'N/A')
                                })
                            
                            # Build graph with FIXED constructor
                            tx_df = pd.DataFrame(transactions_data)
                            graph_snapshot = self.graph_constructor.build_graph_from_transactions(
                                tx_df, window_start, window_end
                            )
                            
                            total_nodes += graph_snapshot.num_nodes
                            total_edges += graph_snapshot.num_edges
                            
                            # Output and export with FIXED methods
                            self._output_graph_snapshot(graph_snapshot, batch_id, windows_processed)
                            self._export_to_neo4j_safe(graph_snapshot, batch_id, windows_processed)
                            
                            windows_processed += 1
                            self.logger.info(f"   ✅ Graph: {graph_snapshot.num_nodes} nodes, {graph_snapshot.num_edges} edges")
                        else:
                            # Handle window with no transactions
                            empty_snapshot = self.graph_constructor.create_empty_graph(window_start, window_end)
                            self._output_graph_snapshot(empty_snapshot, batch_id, windows_processed)
                            windows_processed += 1
                            
                    except Exception as window_error:
                        self.logger.error(f"   ⚠️ Window processing error: {str(window_error)}")
                        continue
                
                # Batch summary
                batch_time = (time.time() - batch_start_time) * 1000
                self.logger.info(f"✅ Batch {batch_id} completed:")
                self.logger.info(f"   📊 Windows: {windows_processed}/{row_count}")
                self.logger.info(f"   🔄 Transactions: {total_transactions}")
                self.logger.info(f"   🕸️ Total Graph: {total_nodes} nodes, {total_edges} edges")
                self.logger.info(f"   ⏱️ Processing time: {batch_time:.2f}ms")
                
            else:
                # Handle empty batch
                empty_snapshot = self.graph_constructor.create_empty_graph()
                self._output_graph_snapshot(empty_snapshot, batch_id, 0)
                self.logger.info(f"✅ Batch {batch_id}: empty, created placeholder")
            
        except Exception as e:
            self.logger.error(f"❌ CRITICAL: Batch {batch_id} failed: {str(e)}", exc_info=True)
    
    def _export_to_neo4j_safe(self, snapshot: FixedGraphSnapshot, batch_id: int, window_id: int):
        """SAFE Neo4j export with comprehensive error handling"""
        try:
            if snapshot.num_nodes == 0:
                return
            
            try:
                from neo4j import GraphDatabase
                
                # Create optimized node and edge data
                nodes_data = []
                for idx in range(snapshot.num_nodes):
                    if idx in self.graph_constructor.index_to_address:
                        address = self.graph_constructor.index_to_address[idx]
                        nodes_data.append({
                            'address': address,
                            'batch_id': batch_id,
                            'window_id': window_id,
                            'window_start': snapshot.window_start.isoformat() if hasattr(snapshot.window_start, 'isoformat') else str(snapshot.window_start),
                            'window_end': snapshot.window_end.isoformat() if hasattr(snapshot.window_end, 'isoformat') else str(snapshot.window_end),
                            'created_at': datetime.now().isoformat()
                        })
                
                if nodes_data:
                    with GraphDatabase.driver(
                        self.settings.neo4j_uri,
                        auth=(self.settings.neo4j_user, self.settings.neo4j_password),
                        max_connection_pool_size=5,
                        connection_timeout=10
                    ) as driver:
                        with driver.session() as session:
                            with session.begin_transaction() as tx:
                                # Optimized batch node insertion
                                tx.run("""
                                    UNWIND $nodes AS node
                                    MERGE (a:Address {address: node.address})
                                    SET a.last_seen_batch = node.batch_id,
                                        a.last_seen_window = node.window_id,
                                        a.last_window_start = node.window_start,
                                        a.last_window_end = node.window_end,
                                        a.last_updated = node.created_at
                                """, nodes=nodes_data)
                                
                                # Add edges if available
                                if hasattr(self.graph_constructor, 'current_edges') and self.graph_constructor.current_edges:
                                    edges_data = []
                                    for edge in self.graph_constructor.current_edges[:100]:  # Limit edges
                                        edges_data.append({
                                            'from_addr': edge.source,
                                            'to_addr': edge.target,
                                            'weight': edge.weight,
                                            'count': edge.count,
                                            'batch_id': batch_id,
                                            'window_id': window_id
                                        })
                                    
                                    if edges_data:
                                        tx.run("""
                                            UNWIND $edges AS edge
                                            MATCH (from:Address {address: edge.from_addr})
                                            MATCH (to:Address {address: edge.to_addr})
                                            MERGE (from)-[r:TRANSACTS_WITH {
                                                batch_id: edge.batch_id, 
                                                window_id: edge.window_id
                                            }]->(to)
                                            SET r.weight = edge.weight,
                                                r.transaction_count = edge.count,
                                                r.last_updated = datetime()
                                        """, edges=edges_data)
                                        
                                        self.logger.info(f"   ✅ Neo4j: {len(edges_data)} edges exported")
                
                    self.logger.info(f"   ✅ Neo4j: {len(nodes_data)} nodes exported")
                    
            except ImportError:
                self.logger.warning("   ⚠️ Neo4j driver not available")
            except Exception as neo4j_error:
                self.logger.error(f"   ❌ Neo4j export failed: {str(neo4j_error)}")
                
        except Exception as e:
            self.logger.error(f"   ❌ Neo4j export error for batch {batch_id}: {str(e)}")
    
    def _output_graph_snapshot(self, snapshot: FixedGraphSnapshot, batch_id: int, window_id: int):
        """Output FIXED graph snapshot with enhanced monitoring"""
        try:
            # Create comprehensive output message
            output_message = {
                "batch_id": batch_id,
                "window_id": window_id,
                "num_nodes": snapshot.num_nodes,
                "num_edges": snapshot.num_edges,
                "construction_time_ms": snapshot.construction_time_ms,
                "processing_timestamp": datetime.now().isoformat(),
                "window_start": str(snapshot.window_start) if snapshot.window_start else None,
                "window_end": str(snapshot.window_end) if snapshot.window_end else None,
                "processing_stats": snapshot.processing_stats,
                "graph_density": snapshot.num_edges / max(snapshot.num_nodes * (snapshot.num_nodes - 1), 1) if snapshot.num_nodes > 1 else 0,
                "avg_degree": (2 * snapshot.num_edges) / max(snapshot.num_nodes, 1) if snapshot.num_nodes > 0 else 0
            }
            
            # Send to Kafka with enhanced error handling
            try:
                self._send_to_kafka(output_message)
            except Exception as kafka_error:
                self.logger.error(f"   ❌ Kafka send failed: {str(kafka_error)}")
            
            # Save to file as backup
            try:
                self._save_to_file(output_message, batch_id, window_id)
            except Exception as file_error:
                self.logger.error(f"   ❌ File save failed: {str(file_error)}")
            
            self.logger.info(f"   📊 Snapshot ready: {snapshot.num_nodes}N, {snapshot.num_edges}E")
                
        except Exception as e:
            self.logger.error(f"   ❌ Output snapshot error: {str(e)}")
    
    def _send_to_kafka(self, message: dict):
        """Send message to Kafka with FIXED producer configuration"""
        try:
            from kafka import KafkaProducer
            import json
            
            producer = KafkaProducer(
                bootstrap_servers=self.settings.kafka_bootstrap_servers,
                value_serializer=lambda x: json.dumps(x).encode('utf-8'),
                key_serializer=lambda x: str(x).encode('utf-8') if x else None,
                retries=3,
                request_timeout_ms=10000,
                delivery_timeout_ms=30000,
                batch_size=16384,
                linger_ms=100
            )
            
            # Use batch_id as key for partitioning
            key = f"batch_{message['batch_id']}_window_{message['window_id']}"
            
            future = producer.send(
                self.settings.kafka_output_topic, 
                value=message,
                key=key
            )
            
            producer.flush(timeout=5)
            record_metadata = future.get(timeout=5)
            producer.close()
            
            self.logger.info(f"   ✅ Kafka: sent to {self.settings.kafka_output_topic}[{record_metadata.partition}]:{record_metadata.offset}")
            
        except ImportError:
            self.logger.warning("   ⚠️ kafka-python not available")
        except Exception as e:
            self.logger.error(f"   ❌ Kafka producer error: {str(e)}")
            raise
    
    def _save_to_file(self, message: dict, batch_id: int, window_id: int):
        """Save graph snapshot to file as backup"""
        try:
            import json
            
            filename = f"graph_snapshot_batch_{batch_id}_window_{window_id}_{int(time.time())}.json"
            filepath = os.path.join(self.settings.file_output_path, filename)
            
            with open(filepath, 'w') as f:
                json.dump(message, f, indent=2, default=str)
            
            self.logger.info(f"   💾 File: saved to {filepath}")
            
        except Exception as e:
            self.logger.error(f"   ❌ File save error: {str(e)}")
    
    def _check_kafka_connectivity(self) -> bool:
        """Check Kafka connectivity and topic availability"""
        try:
            from kafka import KafkaConsumer
            from kafka.errors import KafkaError
            
            self.logger.info("🔍 Checking Kafka connectivity...")
            
            # Quick connectivity test
            consumer = KafkaConsumer(
                bootstrap_servers=self.settings.kafka_bootstrap_servers,
                consumer_timeout_ms=5000,
                request_timeout_ms=10000
            )
            
            # List topics
            topics = consumer.topics()
            consumer.close()
            
            if self.settings.kafka_input_topic in topics:
                self.logger.info(f"   ✅ Topic '{self.settings.kafka_input_topic}' is available")
                self.logger.info(f"   📋 Available topics: {sorted(list(topics))}")
                return True
            else:
                self.logger.error(f"   ❌ Topic '{self.settings.kafka_input_topic}' not found")
                self.logger.info(f"   📋 Available topics: {sorted(list(topics))}")
                return False
                
        except ImportError:
            self.logger.warning("   ⚠️ kafka-python not available for connectivity check")
            return True  # Assume it's available
        except Exception as e:
            self.logger.error(f"   ❌ Kafka connectivity check failed: {str(e)}")
            return False
    
    def _get_topic_info(self) -> dict:
        """Get detailed information about the input topic"""
        try:
            from kafka import KafkaConsumer
            from kafka.structs import TopicPartition
            
            consumer = KafkaConsumer(
                bootstrap_servers=self.settings.kafka_bootstrap_servers,
                consumer_timeout_ms=5000
            )
            
            topic_info = {}
            
            # Get partitions
            partitions = consumer.partitions_for_topic(self.settings.kafka_input_topic)
            if partitions:
                topic_info['partitions'] = sorted(list(partitions))
                
                # Get partition details
                partition_info = {}
                for partition in partitions:
                    tp = TopicPartition(self.settings.kafka_input_topic, partition)
                    consumer.assign([tp])
                    
                    # Get earliest and latest offsets
                    earliest = consumer.beginning_offsets([tp])[tp]
                    latest = consumer.end_offsets([tp])[tp]
                    
                    partition_info[partition] = {
                        'earliest_offset': earliest,
                        'latest_offset': latest,
                        'message_count': latest - earliest
                    }
                
                topic_info['partition_details'] = partition_info
                topic_info['total_messages'] = sum(info['message_count'] for info in partition_info.values())
            
            consumer.close()
            return topic_info
            
        except Exception as e:
            self.logger.error(f"Failed to get topic info: {str(e)}")
            return {}
    
    def run_fixed_streaming_job(self) -> Optional[StreamingQuery]:
        """Run COMPLETELY FIXED streaming job with comprehensive monitoring"""
        
        self.logger.info("🚀 Starting COMPLETELY FIXED streaming job")
        
        try:
            # Pre-flight checks
            self.logger.info("🔍 Running pre-flight checks...")
            
            # Check Kafka connectivity
            if not self._check_kafka_connectivity():
                self.logger.error("❌ Kafka connectivity check failed")
                return None
            
            # Get topic information
            topic_info = self._get_topic_info()
            if topic_info:
                self.logger.info(f"📊 Topic info: {json.dumps(topic_info, indent=2)}")
                
                total_messages = topic_info.get('total_messages', 0)
                if total_messages == 0:
                    self.logger.warning("⚠️ No messages available in topic - streaming will wait for new data")
                else:
                    self.logger.info(f"📨 {total_messages} messages available in topic")
            
            # Create FIXED source
            self.logger.info("📡 Creating FIXED Kafka source...")
            source_df = self._create_fixed_kafka_source()
            
            # Apply FIXED windowing
            self.logger.info("🪟 Applying FIXED windowing...")
            windowed_df = self._apply_fixed_windowing(source_df)
            
            # Prepare checkpoint location
            checkpoint_location = self.settings.get_checkpoint_location("main")
            self.logger.info(f"💾 Checkpoint location: {checkpoint_location}")
            
            # Start streaming with FIXED configuration
            self.logger.info("⚡ Starting streaming query...")
            
            query_builder = windowed_df.writeStream \
                .trigger(processingTime=self.settings.trigger_processing_time) \
                .option("checkpointLocation", checkpoint_location) \
                .option("truncate", "false") \
                .foreachBatch(self._process_fixed_window_batch)
            
            # Add query name for monitoring
            query = query_builder.queryName("fixed-graph-construction").start()
            
            # Log startup success
            self.logger.info("✅ FIXED streaming query started successfully!")
            self.logger.info(f"   🆔 Query ID: {query.id}")
            self.logger.info(f"   📥 Input topic: {self.settings.kafka_input_topic}")
            self.logger.info(f"   📤 Output topic: {self.settings.kafka_output_topic}")
            self.logger.info(f"   💾 Checkpoint: {checkpoint_location}")
            self.logger.info(f"   ⚡ Trigger: {self.settings.trigger_processing_time}")
            self.logger.info(f"   🔄 Max offsets/trigger: {self.settings.max_offsets_per_trigger}")
            
            # Monitor query health
            self._monitor_query_health(query)
            
            return query
            
        except Exception as e:
            self.logger.error(f"❌ CRITICAL: Failed to start streaming job: {str(e)}", exc_info=True)
            raise
    
    def _monitor_query_health(self, query: StreamingQuery):
        """Monitor streaming query health with detailed metrics"""
        def log_progress():
            try:
                progress = query.lastProgress
                if progress:
                    self.logger.info("📈 Query Progress:")
                    self.logger.info(f"   📊 Batch ID: {progress.get('batchId', 'N/A')}")
                    self.logger.info(f"   📥 Input Rows: {progress.get('inputRowsPerSecond', 0):.2f}/sec")
                    self.logger.info(f"   🔄 Processing Rate: {progress.get('processedRowsPerSecond', 0):.2f}/sec")
                    self.logger.info(f"   ⏱️ Batch Duration: {progress.get('batchDuration', 0)}ms")
                    
                    # Check for any issues
                    if progress.get('inputRowsPerSecond', 0) == 0:
                        self.logger.warning("⚠️ No input data detected - waiting for new messages")
                    
                    sources = progress.get('sources', [])
                    for source in sources:
                        if source.get('description', '').startswith('KafkaV2'):
                            offset_info = source.get('endOffset', {})
                            self.logger.info(f"   📍 Kafka offsets: {offset_info}")
            except Exception as e:
                self.logger.error(f"Error getting query progress: {e}")
        
        # Log initial progress
        log_progress()
        
        # Schedule periodic progress logging
        import threading
        def periodic_logging():
            time.sleep(30)  # Wait 30 seconds before first check
            while query.isActive:
                log_progress()
                time.sleep(60)  # Check every minute
        
        progress_thread = threading.Thread(target=periodic_logging, daemon=True)
        progress_thread.start()
    
    def stop(self):
        """Stop streaming application with graceful shutdown"""
        self.logger.info("🛑 Stopping FIXED Graph Construction Layer")
        
        try:
            if self.spark:
                # Stop all active streams
                active_streams = self.spark.streams.active
                for stream in active_streams:
                    self.logger.info(f"   🛑 Stopping stream: {stream.name}")
                    stream.stop()
                
                # Stop Spark session
                self.spark.stop()
                self.logger.info("   ✅ Spark session stopped")
                
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")
        
        self.logger.info("✅ FIXED Graph Construction Layer stopped")


def main():
    """Main entry point with COMPREHENSIVE error handling and recovery"""
    
    logger.info("🚀 Starting COMPLETELY FIXED Graph Construction Layer")
    
    # Initialize with comprehensive configuration
    try:
        builder = FixedSparkGraphBuilder()
        
        # Display startup banner
        logger.info("=" * 60)
        logger.info("    FIXED AML GRAPH CONSTRUCTION LAYER")
        logger.info("    Comprehensive fixes applied for:")
        logger.info("    ✅ Checkpoint management")  
        logger.info("    ✅ Kafka consumer group issues")
        logger.info("    ✅ Column reference problems")
        logger.info("    ✅ Schema conflicts")
        logger.info("    ✅ Error handling")
        logger.info("    ✅ Monitoring and debugging")
        logger.info("=" * 60)
        
        # Start the streaming job
        query = builder.run_fixed_streaming_job()
        
        if query:
            logger.info("🎯 Streaming job is running - monitoring for data...")
            logger.info("💡 Tips:")
            logger.info("   - Check if ingestion is producing new data")
            logger.info("   - Monitor Kafka topic for new messages")
            logger.info("   - Use RESET_CHECKPOINT=true to reprocess data")
            logger.info("   - Set STARTING_OFFSETS=earliest to read from beginning")
            logger.info("")
            logger.info("🔍 Waiting for transactions... (Press Ctrl+C to stop)")
            
            # Wait for termination with periodic status updates
            start_time = time.time()
            try:
                while query.isActive:
                    time.sleep(30)  # Check every 30 seconds
                    elapsed = int(time.time() - start_time)
                    logger.info(f"⏱️ Running for {elapsed//60}m {elapsed%60}s - Query active: {query.isActive}")
                    
                    # Check if query has processed any data
                    progress = query.lastProgress
                    if progress and progress.get('batchId', 0) > 0:
                        logger.info(f"📊 Processed {progress.get('batchId', 0)} batches so far")
                    
            except KeyboardInterrupt:
                logger.info("🛑 Received interrupt signal - stopping gracefully...")
                query.stop()
                
            query.awaitTermination()
        else:
            logger.error("❌ Failed to start streaming query")
            return 1
            
    except KeyboardInterrupt:
        logger.info("🛑 Received interrupt signal during startup")
    except Exception as e:
        logger.error(f"❌ CRITICAL APPLICATION ERROR: {str(e)}", exc_info=True)
        return 1
    finally:
        try:
            builder.stop()
        except:
            pass
        logger.info("🏁 Application shutdown complete")
    
    return 0


if __name__ == "__main__":
    exit(main())