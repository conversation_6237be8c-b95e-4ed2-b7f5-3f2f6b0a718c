"""
Spark Graph Builder Settings - Configuration Management
"""

import os
import logging
from dataclasses import dataclass
from typing import Optional

logger = logging.getLogger(__name__)

@dataclass
class GraphLayerSettings:
    """Graph construction layer settings"""
    
    # Kafka settings
    kafka_bootstrap_servers: str = "kafka:29092"
    kafka_input_topic: str = "filtered-transactions" 
    kafka_output_topic: str = "graph-snapshots"
    kafka_consumer_group: str = "graph-construction-group"
    
    # Neo4j settings
    neo4j_uri: str = "bolt://neo4j:7687"
    neo4j_user: str = "neo4j"
    neo4j_password: str = "blockchain-aml-2024"
    
    # Spark settings
    spark_executor_memory: str = "4g"
    spark_driver_memory: str = "2g"
    checkpoint_location: str = "/app/checkpoints"
    
    # Window settings
    window_duration: str = "1 minute"
    sliding_duration: str = "30 seconds"
    trigger_processing_time: str = "30 seconds"
    watermark_delay: str = "1 hour"  # Allow 1 hour for late data to handle historical data
    
    # Graph settings
    max_nodes_per_graph: int = 8000
    max_edges_per_graph: int = 16000
    edge_weight_threshold: float = 0.0005
    include_self_loops: bool = False
    node_feature_dim: int = 64
    include_temporal_features: bool = True
    include_statistical_features: bool = True
    
    # Performance settings
    enable_detailed_metrics: bool = True
    output_format: str = "both"  # neo4j, kafka, file, both
    max_offsets_per_trigger: int = 1000
    enable_vectorized_operations: bool = True
    enable_feature_caching: bool = True
    enable_graph_pruning: bool = True
    batch_size: int = 100
    enable_performance_logging: bool = True
    
    def __post_init__(self):
        """Load settings from environment variables"""
        self.kafka_bootstrap_servers = os.getenv("KAFKA_BOOTSTRAP_SERVERS", self.kafka_bootstrap_servers)
        self.kafka_input_topic = os.getenv("KAFKA_INPUT_TOPIC", self.kafka_input_topic)
        self.kafka_output_topic = os.getenv("KAFKA_OUTPUT_TOPIC", self.kafka_output_topic)
        self.kafka_consumer_group = os.getenv("KAFKA_CONSUMER_GROUP", self.kafka_consumer_group)
        
        self.neo4j_uri = os.getenv("NEO4J_URI", self.neo4j_uri)
        self.neo4j_user = os.getenv("NEO4J_USER", self.neo4j_user)
        self.neo4j_password = os.getenv("NEO4J_PASSWORD", self.neo4j_password)
        
        self.spark_executor_memory = os.getenv("SPARK_EXECUTOR_MEMORY", self.spark_executor_memory)
        self.spark_driver_memory = os.getenv("SPARK_DRIVER_MEMORY", self.spark_driver_memory)
        self.checkpoint_location = os.getenv("CHECKPOINT_LOCATION", self.checkpoint_location)
        
        self.window_duration = os.getenv("WINDOW_DURATION", self.window_duration)
        self.sliding_duration = os.getenv("SLIDING_DURATION", self.sliding_duration)
        self.watermark_delay = os.getenv("WATERMARK_DELAY", self.watermark_delay)
        
        self.max_nodes_per_graph = int(os.getenv("MAX_NODES_PER_GRAPH", self.max_nodes_per_graph))
        
        self.enable_detailed_metrics = os.getenv("ENABLE_DETAILED_METRICS", "true").lower() == "true"
    
    @classmethod
    def from_env(cls) -> 'GraphLayerSettings':
        """Create settings from environment variables"""
        return cls()

@dataclass 
class KafkaConfig:
    """Kafka connection configuration"""
    bootstrap_servers: str = "kafka:29092"
    input_topic: str = "filtered-transactions"
    output_topic: str = "graph-snapshots"
    consumer_group: str = "graph-construction-group"

@dataclass
class SparkConfig:
    """Spark session configuration"""
    app_name: str = "Real-Time-AML-Graph-Construction"
    master: str = "local[*]"
    executor_memory: str = "4g"
    driver_memory: str = "2g"

@dataclass
class PerformanceMonitor:
    """Performance monitoring settings"""
    enable_metrics: bool = True
    log_level: str = "INFO"
    metrics_interval: int = 30
