"""
Fixed Configuration Settings for Graph Construction Layer
Resolves all schema conflicts and environment variable issues
"""

import os
import logging
from dataclasses import dataclass
from typing import Optional, Dict, Any


@dataclass
class GraphLayerSettings:
    """
    Fixed Configuration Settings for Graph Construction Layer
    Addresses all schema conflicts and field mapping issues
    """
    
    # Kafka Configuration - Fixed
    kafka_bootstrap_servers: str = "kafka:29092"
    kafka_input_topic: str = "filtered-transactions" 
    kafka_output_topic: str = "graph-snapshots"
    kafka_consumer_group: str = "graph-construction-group"
    
    # Spark Configuration - Optimized for ARM64
    spark_executor_memory: str = "1g"
    spark_driver_memory: str = "512m"
    spark_driver_max_result_size: str = "256m"
    max_offsets_per_trigger: int = 1000
    trigger_processing_time: str = "30 seconds"
    
    # Window Configuration - Fixed timing
    window_duration: str = "1 minute"
    sliding_duration: str = "30 seconds"
    watermark_delay: str = "2 minutes"
    
    # Neo4j Configuration
    neo4j_uri: str = "bolt://neo4j:7687"
    neo4j_user: str = "neo4j"
    neo4j_password: str = "blockchain-aml-2024"
    
    # Graph Construction Configuration - Fixed
    max_nodes_per_graph: int = 8000
    edge_weight_threshold: float = 0.0005
    include_self_loops: bool = False
    node_feature_dim: int = 64
    include_temporal_features: bool = True
    include_statistical_features: bool = True
    
    # Performance Configuration - Optimized
    enable_vectorized_operations: bool = True
    enable_feature_caching: bool = True
    enable_graph_pruning: bool = True
    enable_detailed_metrics: bool = True
    enable_performance_logging: bool = True
    batch_size: int = 1000
    
    # Output Configuration
    output_format: str = "both"  # "kafka", "file", "both"
    file_output_path: str = "/tmp/graph-snapshots"
    enable_compression: bool = False
    
    # Checkpoint Configuration - Fixed paths
    checkpoint_location: str = "/tmp/spark-checkpoints"
    
    def __post_init__(self):
        """Apply environment variable overrides after initialization"""
        self._apply_env_overrides()
        self._validate_settings()
        self._create_directories()
    
    def _apply_env_overrides(self):
        """Apply environment variable overrides with proper type conversion"""
        env_mappings = {
            # Kafka settings
            "KAFKA_BOOTSTRAP_SERVERS": ("kafka_bootstrap_servers", str),
            "KAFKA_INPUT_TOPIC": ("kafka_input_topic", str),
            "KAFKA_OUTPUT_TOPIC": ("kafka_output_topic", str),
            "KAFKA_CONSUMER_GROUP": ("kafka_consumer_group", str),
            
            # Spark settings
            "SPARK_EXECUTOR_MEMORY": ("spark_executor_memory", str),
            "SPARK_DRIVER_MEMORY": ("spark_driver_memory", str),
            "MAX_OFFSETS_PER_TRIGGER": ("max_offsets_per_trigger", int),
            "TRIGGER_PROCESSING_TIME": ("trigger_processing_time", str),
            
            # Window settings
            "WINDOW_DURATION": ("window_duration", str),
            "SLIDING_DURATION": ("sliding_duration", str),
            "WATERMARK_DELAY": ("watermark_delay", str),
            
            # Neo4j settings
            "NEO4J_URI": ("neo4j_uri", str),
            "NEO4J_USER": ("neo4j_user", str),
            "NEO4J_PASSWORD": ("neo4j_password", str),
            
            # Graph settings
            "MAX_NODES_PER_GRAPH": ("max_nodes_per_graph", int),
            "EDGE_WEIGHT_THRESHOLD": ("edge_weight_threshold", float),
            "NODE_FEATURE_DIM": ("node_feature_dim", int),
            
            # Performance settings
            "ENABLE_DETAILED_METRICS": ("enable_detailed_metrics", lambda x: x.lower() == "true"),
            "BATCH_SIZE": ("batch_size", int),
            
            # Output settings
            "OUTPUT_FORMAT": ("output_format", str),
            "FILE_OUTPUT_PATH": ("file_output_path", str),
            "CHECKPOINT_LOCATION": ("checkpoint_location", str)
        }
        
        for env_var, (attr_name, type_converter) in env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value is not None:
                try:
                    converted_value = type_converter(env_value)
                    setattr(self, attr_name, converted_value)
                except (ValueError, TypeError) as e:
                    logging.warning(f"Invalid value for {env_var}: {env_value}, using default. Error: {e}")
    
    def _validate_settings(self):
        """Validate configuration settings"""
        logger = logging.getLogger(__name__)
        
        # Validate required settings
        required_settings = [
            ("kafka_bootstrap_servers", self.kafka_bootstrap_servers),
            ("kafka_input_topic", self.kafka_input_topic),
            ("neo4j_uri", self.neo4j_uri),
            ("neo4j_user", self.neo4j_user),
            ("neo4j_password", self.neo4j_password)
        ]
        
        for name, value in required_settings:
            if not value or value.strip() == "":
                raise ValueError(f"Required setting {name} is empty or not set")
        
        # Validate numeric settings
        if self.max_offsets_per_trigger <= 0:
            raise ValueError("max_offsets_per_trigger must be positive")
        
        if self.batch_size <= 0:
            raise ValueError("batch_size must be positive")
        
        if self.edge_weight_threshold < 0:
            raise ValueError("edge_weight_threshold cannot be negative")
        
        logger.info("✅ Configuration validation passed")
    
    def _create_directories(self):
        """Create required directories"""
        directories = [
            self.checkpoint_location,
            self.file_output_path
        ]
        
        for directory in directories:
            if directory and directory.strip():
                try:
                    os.makedirs(directory.strip(), exist_ok=True)
                except Exception as e:
                    logging.warning(f"Could not create directory {directory}: {e}")
    
    @classmethod
    def from_env(cls) -> 'GraphLayerSettings':
        """Create settings instance with environment variable overrides"""
        return cls()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert settings to dictionary for logging/debugging"""
        return {
            attr: getattr(self, attr) 
            for attr in dir(self) 
            if not attr.startswith('_') and not callable(getattr(self, attr))
        }
    
    def log_settings(self):
        """Log current settings for debugging"""
        logger = logging.getLogger(__name__)
        logger.info("🔧 Current Configuration Settings:")
        
        settings_dict = self.to_dict()
        for key, value in sorted(settings_dict.items()):
            # Mask sensitive information
            if 'password' in key.lower() or 'secret' in key.lower():
                displayed_value = '*' * len(str(value)) if value else 'None'
            else:
                displayed_value = value
            logger.info(f"   {key}: {displayed_value}")


@dataclass
class FixedSchemaDefinitions:
    """Fixed schema definitions to resolve field conflicts"""
    
    @staticmethod
    def get_transaction_schema():
        """Get transaction schema with fixed field names"""
        from pyspark.sql.types import StructType, StructField, StringType, DoubleType, LongType, BooleanType
        
        return StructType([
            StructField("txId", StringType(), True),
            StructField("fromAddr", StringType(), True),
            StructField("toAddr", StringType(), True),
            StructField("valueEth", DoubleType(), True),
            StructField("blockNumber", LongType(), True),
            StructField("timestamp", LongType(), True),
            StructField("gasPrice", LongType(), True),
            StructField("gasUsed", LongType(), True),
            StructField("isContract", BooleanType(), True),
            StructField("roundAmount", BooleanType(), True),
            StructField("highValue", BooleanType(), True),
            StructField("microAmount", BooleanType(), True),
            StructField("riskScore", StringType(), True),  # Transaction-level
            StructField("reason", StringType(), True)      # Transaction-level
        ])
    
    @staticmethod
    def get_alert_schema():
        """Get alert schema with conflict resolution"""
        from pyspark.sql.types import StructType, StructField, StringType, DoubleType, ArrayType
        
        return StructType([
            StructField("alertId", StringType(), True),
            StructField("patternName", StringType(), True),
            StructField("severity", StringType(), True),
            StructField("riskScore", StringType(), True),  # Alert-level (will be renamed)
            StructField("reason", StringType(), True),     # Alert-level (will be renamed)
            StructField("involvedTransactions", ArrayType(FixedSchemaDefinitions.get_transaction_schema()), True),
            StructField("primaryAddr", StringType(), True),
            StructField("totalValueEth", DoubleType(), True),
            StructField("detectionTimestamp", StringType(), True)
        ])
    
    @staticmethod
    def get_field_mappings():
        """Get field mappings to resolve conflicts"""
        return {
            # Alert-level fields (renamed to avoid conflicts)
            "alert_fields": {
                "riskScore": "alert_risk_score",
                "reason": "alert_reason"
            },
            # Transaction-level fields (prefixed to be clear)
            "transaction_fields": {
                "riskScore": "tx_risk_score", 
                "reason": "tx_reason"
            },
            # Address field standardization
            "address_fields": {
                "fromAddr": "from_address",
                "toAddr": "to_address"
            }
        }


class EnvironmentValidator:
    """Validate environment setup for the application"""
    
    @staticmethod
    def validate_environment() -> bool:
        """Validate that all required environment components are available"""
        logger = logging.getLogger(__name__)
        
        validation_results = []
        
        # Check Python packages
        required_packages = [
            ("pyspark", "PySpark for streaming"),
            ("pandas", "Pandas for data processing"),
            ("torch", "PyTorch for graph construction"),
            ("numpy", "NumPy for numerical operations")
        ]
        
        for package, description in required_packages:
            try:
                __import__(package)
                validation_results.append((f"✅ {package}", description))
            except ImportError:
                validation_results.append((f"❌ {package}", f"MISSING: {description}"))
        
        # Check environment variables
        critical_env_vars = [
            ("KAFKA_BOOTSTRAP_SERVERS", "Kafka connection"),
            ("NEO4J_URI", "Neo4j database connection"),
            ("NEO4J_PASSWORD", "Neo4j authentication")
        ]
        
        for env_var, description in critical_env_vars:
            value = os.getenv(env_var)
            if value:
                validation_results.append((f"✅ {env_var}", f"{description}: {value}"))
            else:
                validation_results.append((f"⚠️ {env_var}", f"NOT SET: {description} (using default)"))
        
        # Log validation results
        logger.info("🔍 Environment Validation Results:")
        all_good = True
        for status, details in validation_results:
            logger.info(f"   {status}: {details}")
            if status.startswith("❌"):
                all_good = False
        
        return all_good


# Export convenience function
def get_fixed_settings() -> GraphLayerSettings:
    """Get properly configured settings with all fixes applied"""
    return GraphLayerSettings.from_env()


# Export validation function  
def validate_environment() -> bool:
    """Validate the current environment setup"""
    return EnvironmentValidator.validate_environment()


if __name__ == "__main__":
    # Test configuration loading
    logging.basicConfig(level=logging.INFO)
    
    print("🧪 Testing Fixed Configuration Settings")
    
    # Validate environment
    if validate_environment():
        print("✅ Environment validation passed")
    else:
        print("⚠️ Some environment issues detected")
    
    # Load and test settings
    try:
        settings = get_fixed_settings()
        settings.log_settings()
        print("✅ Configuration loaded successfully")
        
        # Test field mappings
        field_mappings = FixedSchemaDefinitions.get_field_mappings()
        print(f"✅ Field mappings loaded: {len(field_mappings)} categories")
        
    except Exception as e:
        print(f"❌ Configuration loading failed: {e}")
        import traceback
        traceback.print_exc()