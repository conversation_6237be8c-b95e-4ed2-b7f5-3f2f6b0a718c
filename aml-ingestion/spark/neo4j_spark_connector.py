#!/usr/bin/env python3
"""
Neo4j Spark Connector集成 - 修复版本
Neo4j Spark Connector Integration for AML Pipeline - Fixed Version

修复了ClassNotFoundException和调用链问题
"""

import os
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import json

from pyspark.sql import SparkSession, DataFrame
from pyspark.sql.functions import *
from pyspark.sql.types import *
import pyspark.sql.functions as F
from pyspark.sql.streaming import StreamingQuery

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class Neo4jSparkConnector:
    """Neo4j Spark Connector集成类 - 修复版本"""
    
    def __init__(self):
        # Spark配置 - 修复JAR包依赖
        self.spark_config = {
            'spark.app.name': 'AML-Neo4j-Spark-Connector',
            'spark.master': 'local[*]',
            'spark.sql.adaptive.enabled': 'true',
            'spark.sql.adaptive.coalescePartitions.enabled': 'true',
            'spark.sql.streaming.checkpointLocation': '/tmp/spark-checkpoints',
            
            # Neo4j连接配置
            'neo4j.url': 'bolt://neo4j:7687',
            'neo4j.authentication.type': 'basic',
            'neo4j.authentication.basic.username': 'neo4j',
            'neo4j.authentication.basic.password': 'blockchain-aml-2024',
            
            # 性能优化
            'spark.sql.streaming.forceDeleteTempCheckpointLocation': 'true',
            'spark.sql.streaming.stateStore.providerClass': 'org.apache.spark.sql.execution.streaming.state.HDFSBackedStateStoreProvider',
            'spark.serializer': 'org.apache.spark.serializer.KryoSerializer',
            'spark.sql.adaptive.skewJoin.enabled': 'true'
        }
        
        # Kafka配置
        self.kafka_config = {
            'kafka.bootstrap.servers': 'kafka:29092',
            'subscribe': 'filtered-transactions',
            'startingOffsets': 'latest',
            'maxOffsetsPerTrigger': 1000,
            'failOnDataLoss': 'false'
        }
        
        # 修复的Maven坐标
        self.maven_packages = [
            'org.neo4j:neo4j-connector-apache-spark_2.12:5.3.0_for_spark_3.5',
            'org.apache.spark:spark-sql-kafka-0-10_2.12:3.5.0',
            'org.neo4j.driver:neo4j-java-driver:5.14.0'
        ]
        
        self.spark: Optional[SparkSession] = None
        self.active_queries: List[StreamingQuery] = []
    
    def create_spark_session(self) -> SparkSession:
        """创建Spark会话 - 修复JAR包问题"""
        try:
            builder = SparkSession.builder
            
            # 修复的JAR包配置
            builder = builder.config(
                'spark.jars.packages', 
                ','.join(self.maven_packages)
            )
            
            # 添加仓库配置以确保JAR包下载
            builder = builder.config(
                'spark.jars.repositories',
                'https://repo1.maven.org/maven2,https://oss.sonatype.org/content/repositories/releases'
            )
            
            # 应用其他配置
            for key, value in self.spark_config.items():
                if not key.startswith('neo4j.'):
                    builder = builder.config(key, value)
            
            self.spark = builder.getOrCreate()
            self.spark.sparkContext.setLogLevel("WARN")
            
            logger.info("Spark会话创建成功")
            logger.info(f"Spark版本: {self.spark.version}")
            
            # 验证Neo4j连接器是否可用
            self._verify_neo4j_connector()
            
            return self.spark
            
        except Exception as e:
            logger.error(f"创建Spark会话失败: {e}")
            raise
    
    def _verify_neo4j_connector(self) -> None:
        """验证Neo4j连接器是否正确加载"""
        try:
            # 尝试创建一个简单的测试DataFrame来验证连接器
            test_data = [("test", "value")]
            test_df = self.spark.createDataFrame(test_data, ["key", "value"])
            
            # 测试是否可以访问Neo4j格式
            try:
                test_df.write.format("org.neo4j.spark.DataSource").mode("overwrite")
                logger.info("Neo4j连接器验证成功")
            except Exception as e:
                logger.warning(f"Neo4j连接器可能未正确加载: {e}")
                
        except Exception as e:
            logger.error(f"验证Neo4j连接器失败: {e}")
    
    def read_from_kafka_stream(self) -> DataFrame:
        """从Kafka读取流数据"""
        try:
            df = self.spark \
                .readStream \
                .format("kafka") \
                .options(**self.kafka_config) \
                .load()
            
            # 解析JSON数据的Schema
            schema = StructType([
                StructField("txId", StringType(), True),
                StructField("fromAddr", StringType(), True),
                StructField("toAddr", StringType(), True),
                StructField("valueEth", DoubleType(), True),
                StructField("gasPrice", LongType(), True),
                StructField("gasUsed", LongType(), True),
                StructField("blockNumber", LongType(), True),
                StructField("timestamp", LongType(), True)
            ])
            
            # 解析JSON数据并添加处理时间戳
            parsed_df = df.select(
                from_json(col("value").cast("string"), schema).alias("data"),
                col("timestamp").alias("kafka_timestamp")
            ).select(
                col("data.*"),
                col("kafka_timestamp"),
                current_timestamp().alias("processing_time")
            )
            
            logger.info("成功配置Kafka流读取")
            return parsed_df
            
        except Exception as e:
            logger.error(f"配置Kafka流读取失败: {e}")
            raise
    
    def create_sliding_window_graphs(self, stream_df: DataFrame) -> DataFrame:
        """创建滑动窗口图快照 - 修复调用链"""
        try:
            # 转换时间戳并添加水印
            windowed_df = stream_df \
                .withColumn("event_time", to_timestamp(col("timestamp"))) \
                .filter(col("event_time").isNotNull()) \
                .withWatermark("event_time", "30 seconds") \
                .groupBy(
                    window(col("event_time"), "1 minute", "30 seconds"),
                    col("fromAddr"),
                    col("toAddr")
                ) \
                .agg(
                    count("*").alias("transaction_count"),
                    sum("valueEth").alias("total_value"),
                    avg("valueEth").alias("avg_value"),
                    max("valueEth").alias("max_value"),
                    min("valueEth").alias("min_value"),
                    collect_list("txId").alias("transaction_ids"),
                    min("timestamp").alias("window_start_ts"),
                    max("timestamp").alias("window_end_ts"),
                    first("processing_time").alias("processed_at")
                ) \
                .withColumn("window_id", 
                    concat(
                        col("window.start").cast("string"),
                        lit("_"),
                        col("window.end").cast("string")
                    )
                ) \
                .withColumn("window_start", col("window.start")) \
                .withColumn("window_end", col("window.end"))
            
            logger.info("成功配置滑动窗口图构建")
            return windowed_df
            
        except Exception as e:
            logger.error(f"配置滑动窗口图构建失败: {e}")
            raise
    
    def extract_graph_features(self, windowed_df: DataFrame) -> DataFrame:
        """提取图特征用于机器学习 - 增强特征工程"""
        try:
            # 计算窗口持续时间（秒）
            duration_col = (col("window_end_ts") - col("window_start_ts")) / 1000.0
            
            features_df = windowed_df \
                .withColumn("window_duration", duration_col) \
                .withColumn("velocity_score", 
                    when(col("window_duration") > 0, 
                         col("total_value") / col("window_duration"))
                    .otherwise(0.0)) \
                .withColumn("frequency_score", 
                    when(col("window_duration") > 0,
                         col("transaction_count") / col("window_duration"))
                    .otherwise(0.0)) \
                .withColumn("value_variance", 
                    col("max_value") - col("min_value")) \
                .withColumn("regularity_score", 
                    when(col("transaction_count") > 1, 
                         abs(col("avg_value") - (col("total_value") / col("transaction_count"))))
                    .otherwise(0.0)) \
                .withColumn("concentration_ratio",
                    when(col("total_value") > 0,
                         col("max_value") / col("total_value"))
                    .otherwise(0.0)) \
                .withColumn("risk_indicators", 
                    array_remove(
                        array(
                            when(col("total_value") > 100.0, lit("high_value")),
                            when(col("transaction_count") > 10, lit("high_frequency")),
                            when(col("velocity_score") > 10.0, lit("high_velocity")),
                            when(col("concentration_ratio") > 0.8, lit("high_concentration"))
                        ),
                        lit(None)
                    )) \
                .withColumn("risk_score",
                    (col("velocity_score") * 0.3 + 
                     col("frequency_score") * 0.2 + 
                     col("concentration_ratio") * 0.3 +
                     size(col("risk_indicators")) * 0.2))
            
            logger.info("成功配置图特征提取")
            return features_df
            
        except Exception as e:
            logger.error(f"配置图特征提取失败: {e}")
            raise
    
    def write_to_neo4j(self, df: DataFrame, node_label: str = "GraphWindow") -> StreamingQuery:
        """将数据写入Neo4j - 修复写入方法"""
        try:
            def write_batch_to_neo4j(batch_df, batch_id):
                """批量写入Neo4j的函数 - 修复实现"""
                try:
                    if batch_df.count() == 0:
                        logger.info(f"批次 {batch_id} 为空，跳过写入")
                        return
                    
                    logger.info(f"开始处理批次 {batch_id}，记录数: {batch_df.count()}")
                    
                    # 方法1: 使用Neo4j连接器（如果可用）
                    try:
                        batch_df.write \
                            .format("org.neo4j.spark.DataSource") \
                            .mode("append") \
                            .option("url", self.spark_config['neo4j.url']) \
                            .option("authentication.type", "basic") \
                            .option("authentication.basic.username", 
                                   self.spark_config['neo4j.authentication.basic.username']) \
                            .option("authentication.basic.password", 
                                   self.spark_config['neo4j.authentication.basic.password']) \
                            .option("labels", node_label) \
                            .option("node.keys", "fromAddr,toAddr,window_id") \
                            .save()
                        
                        logger.info(f"批次 {batch_id} 通过Neo4j连接器写入成功")
                        
                    except Exception as neo4j_error:
                        logger.warning(f"Neo4j连接器写入失败: {neo4j_error}")
                        # 方法2: 回退到直接JDBC写入或保存到文件
                        self._fallback_write_batch(batch_df, batch_id, node_label)
                        
                except Exception as e:
                    logger.error(f"批次 {batch_id} 处理失败: {e}")
                    # 保存失败的批次到错误目录
                    self._save_error_batch(batch_df, batch_id, str(e))
            
            # 配置流写入
            query = df.writeStream \
                .outputMode("append") \
                .foreachBatch(write_batch_to_neo4j) \
                .option("checkpointLocation", f"/tmp/neo4j-checkpoint-{node_label}") \
                .trigger(processingTime='30 seconds') \
                .start()
            
            self.active_queries.append(query)
            logger.info("成功配置Neo4j流写入")
            return query
            
        except Exception as e:
            logger.error(f"配置Neo4j流写入失败: {e}")
            raise
    
    def _fallback_write_batch(self, batch_df: DataFrame, batch_id: int, node_label: str) -> None:
        """回退写入方法 - 保存到文件系统"""
        try:
            output_path = f"/tmp/neo4j-fallback/{node_label}/batch_{batch_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            batch_df.coalesce(1) \
                .write \
                .mode("overwrite") \
                .option("header", "true") \
                .csv(output_path)
            
            logger.info(f"批次 {batch_id} 回退保存到: {output_path}")
            
        except Exception as e:
            logger.error(f"回退写入失败: {e}")
    
    def _save_error_batch(self, batch_df: DataFrame, batch_id: int, error_msg: str) -> None:
        """保存错误批次"""
        try:
            error_path = f"/tmp/neo4j-errors/batch_{batch_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # 添加错误信息列
            error_df = batch_df.withColumn("error_message", lit(error_msg)) \
                              .withColumn("error_timestamp", current_timestamp())
            
            error_df.coalesce(1) \
                .write \
                .mode("overwrite") \
                .option("header", "true") \
                .csv(error_path)
            
            logger.error(f"错误批次 {batch_id} 保存到: {error_path}")
            
        except Exception as e:
            logger.error(f"保存错误批次失败: {e}")
    
    def export_for_ml_inference(self, features_df: DataFrame) -> DataFrame:
        """导出数据用于机器学习推理 - 增强格式"""
        try:
            # 准备标准化的ML格式数据
            ml_export_df = features_df \
                .select(
                    col("fromAddr").alias("source_node"),
                    col("toAddr").alias("target_node"),
                    col("total_value").alias("edge_weight"),
                    col("transaction_count").alias("edge_frequency"),
                    col("velocity_score").alias("velocity"),
                    col("frequency_score").alias("frequency"),
                    col("value_variance").alias("variance"),
                    col("regularity_score").alias("regularity"),
                    col("concentration_ratio").alias("concentration"),
                    col("risk_score").alias("risk_score"),
                    col("risk_indicators").alias("risk_flags"),
                    col("window_start").alias("window_start"),
                    col("window_end").alias("window_end"),
                    col("window_id").alias("graph_snapshot_id")
                ) \
                .withColumn("export_timestamp", current_timestamp()) \
                .withColumn("feature_version", lit("v2.0"))
            
            logger.info("成功配置ML推理数据导出")
            return ml_export_df
            
        except Exception as e:
            logger.error(f"配置ML推理数据导出失败: {e}")
            raise
    
    def save_ml_export_to_kafka(self, ml_df: DataFrame, output_topic: str = "ml-graph-features") -> StreamingQuery:
        """将ML特征数据保存到Kafka - 修复方法调用链"""
        try:
            # 转换为标准JSON格式
            json_df = ml_df.select(
                to_json(
                    struct(
                        col("source_node"),
                        col("target_node"), 
                        col("edge_weight"),
                        col("edge_frequency"),
                        col("velocity"),
                        col("frequency"),
                        col("variance"),
                        col("regularity"),
                        col("concentration"),
                        col("risk_score"),
                        col("risk_flags"),
                        col("window_start").cast("string").alias("window_start"),
                        col("window_end").cast("string").alias("window_end"),
                        col("graph_snapshot_id"),
                        col("export_timestamp").cast("string").alias("export_timestamp"),
                        col("feature_version")
                    )
                ).alias("value")
            ).withColumn("key", 
                concat(
                    col("source_node"), 
                    lit("_"), 
                    col("target_node"),
                    lit("_"),
                    date_format(current_timestamp(), "yyyyMMddHHmmss")
                )
            )
            
            # 写入Kafka
            query = json_df.writeStream \
                .format("kafka") \
                .option("kafka.bootstrap.servers", "kafka:29092") \
                .option("topic", output_topic) \
                .option("checkpointLocation", f"/tmp/kafka-ml-checkpoint-{output_topic}") \
                .outputMode("append") \
                .trigger(processingTime='1 minute') \
                .start()
            
            self.active_queries.append(query)
            logger.info(f"成功配置ML特征数据Kafka导出到topic: {output_topic}")
            return query
            
        except Exception as e:
            logger.error(f"配置ML特征Kafka导出失败: {e}")
            raise
    
    def create_debug_output_stream(self, df: DataFrame, stream_name: str) -> StreamingQuery:
        """创建调试输出流 - 帮助诊断调用链问题"""
        try:
            query = df.writeStream \
                .outputMode("append") \
                .format("console") \
                .option("truncate", False) \
                .option("numRows", 10) \
                .trigger(processingTime='30 seconds') \
                .queryName(f"debug_{stream_name}") \
                .start()
            
            self.active_queries.append(query)
            logger.info(f"调试流 {stream_name} 启动成功")
            return query
            
        except Exception as e:
            logger.error(f"创建调试流 {stream_name} 失败: {e}")
            raise
    
    def run_streaming_pipeline(self, debug_mode: bool = False) -> None:
        """运行完整的流处理管道 - 修复版本"""
        logger.info("启动Neo4j Spark Connector流处理管道...")
        
        try:
            # 创建Spark会话
            self.create_spark_session()
            
            # 1. 从Kafka读取流数据
            logger.info("步骤1: 配置Kafka流读取")
            stream_df = self.read_from_kafka_stream()
            
            if debug_mode:
                self.create_debug_output_stream(stream_df, "raw_kafka_data")
            
            # 2. 创建滑动窗口图
            logger.info("步骤2: 配置滑动窗口图构建")
            windowed_df = self.create_sliding_window_graphs(stream_df)
            
            if debug_mode:
                self.create_debug_output_stream(windowed_df, "windowed_data")
            
            # 3. 提取图特征
            logger.info("步骤3: 配置图特征提取")
            features_df = self.extract_graph_features(windowed_df)
            
            if debug_mode:
                self.create_debug_output_stream(features_df, "features_data")
            
            # 4. 写入Neo4j
            logger.info("步骤4: 配置Neo4j写入")
            neo4j_query = self.write_to_neo4j(features_df, "GraphWindow")
            
            # 5. 导出ML特征
            logger.info("步骤5: 配置ML特征导出")
            ml_df = self.export_for_ml_inference(features_df)
            
            if debug_mode:
                self.create_debug_output_stream(ml_df, "ml_export_data")
            
            # 6. 保存ML特征到Kafka
            logger.info("步骤6: 配置ML特征Kafka导出")
            ml_query = self.save_ml_export_to_kafka(ml_df)
            
            logger.info("流处理管道启动成功，等待数据...")
            logger.info(f"活跃查询数量: {len(self.active_queries)}")
            
            # 等待所有流处理完成
            for i, query in enumerate(self.active_queries):
                logger.info(f"等待查询 {i+1}/{len(self.active_queries)}: {query.name}")
                query.awaitTermination()
            
        except KeyboardInterrupt:
            logger.info("收到中断信号，正在关闭流处理...")
            self.stop_all_streams()
        except Exception as e:
            logger.error(f"流处理管道运行失败: {e}")
            self.stop_all_streams()
            raise
        finally:
            if self.spark:
                self.spark.stop()
                logger.info("Spark会话已关闭")
    
    def stop_all_streams(self) -> None:
        """停止所有活跃的流查询"""
        logger.info("停止所有流查询...")
        for i, query in enumerate(self.active_queries):
            try:
                if query.isActive:
                    logger.info(f"停止查询 {i+1}: {query.name}")
                    query.stop()
            except Exception as e:
                logger.error(f"停止查询失败: {e}")
        
        self.active_queries.clear()
        logger.info("所有流查询已停止")
    
    def run_batch_analysis(self, hours_back: int = 24) -> Dict[str, Any]:
        """运行批量图分析 - 增强版本"""
        logger.info(f"开始批量图分析，回溯 {hours_back} 小时...")
        
        try:
            # 创建Spark会话
            self.create_spark_session()
            
            # 构建查询时间范围
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=hours_back)
            
            # 尝试从Neo4j读取，如果失败则从其他数据源读取
            try:
                historical_df = self._read_from_neo4j(start_time, end_time)
            except Exception as neo4j_error:
                logger.warning(f"Neo4j读取失败: {neo4j_error}")
                historical_df = self._read_from_fallback_sources(start_time, end_time)
            
            if historical_df is None or historical_df.count() == 0:
                logger.warning("没有找到历史数据")
                return {"error": "No historical data found"}
            
            # 计算详细的图统计信息
            stats = self._calculate_graph_statistics(historical_df, start_time, end_time)
            
            logger.info(f"批量分析完成: {json.dumps(stats, indent=2, default=str)}")
            return stats
            
        except Exception as e:
            logger.error(f"批量图分析失败: {e}")
            raise
        finally:
            if self.spark:
                self.spark.stop()
    
    def _read_from_neo4j(self, start_time: datetime, end_time: datetime) -> DataFrame:
        """从Neo4j读取历史数据"""
        query = f"""
        MATCH (t:Transaction)
        WHERE t.timestamp >= {int(start_time.timestamp())} 
          AND t.timestamp <= {int(end_time.timestamp())}
        RETURN t.tx_id as txId, t.from_addr as fromAddr, t.to_addr as toAddr,
               t.value_eth as valueEth, t.timestamp as timestamp,
               t.gas_used as gasUsed, t.gas_price as gasPrice,
               t.block_number as blockNumber
        """
        
        return self.spark.read \
            .format("org.neo4j.spark.DataSource") \
            .option("url", self.spark_config['neo4j.url']) \
            .option("authentication.type", "basic") \
            .option("authentication.basic.username", 
                   self.spark_config['neo4j.authentication.basic.username']) \
            .option("authentication.basic.password", 
                   self.spark_config['neo4j.authentication.basic.password']) \
            .option("query", query) \
            .load()
    
    def _read_from_fallback_sources(self, start_time: datetime, end_time: datetime) -> Optional[DataFrame]:
        """从回退数据源读取历史数据"""
        try:
            # 尝试从文件系统读取
            fallback_paths = [
                "/tmp/neo4j-fallback/GraphWindow/batch_*",
                "/data/transactions/processed/*.csv",
                "/tmp/transactions/*.parquet"
            ]
            
            for path in fallback_paths:
                try:
                    if "*" in path:
                        df = self.spark.read.option("header", "true").csv(path)
                    elif path.endswith(".parquet"):
                        df = self.spark.read.parquet(path)
                    else:
                        df = self.spark.read.option("header", "true").csv(path)
                    
                    if df.count() > 0:
                        logger.info(f"从 {path} 读取到 {df.count()} 条记录")
                        return df
                        
                except Exception as e:
                    logger.debug(f"尝试读取 {path} 失败: {e}")
                    continue
            
            return None
            
        except Exception as e:
            logger.error(f"回退数据源读取失败: {e}")
            return None
    
    def _calculate_graph_statistics(self, df: DataFrame, start_time: datetime, end_time: datetime) -> Dict[str, Any]:
        """计算详细的图统计信息"""
        try:
            # 基础统计
            total_count = df.count()
            if total_count == 0:
                return {"error": "No data to analyze"}
            
            # 地址统计
            unique_from_addrs = df.select('fromAddr').distinct().count()
            unique_to_addrs = df.select('toAddr').distinct().count()
            unique_addrs = df.select('fromAddr').union(df.select('toAddr')).distinct().count()
            
            # 价值统计
            value_stats = df.agg(
                sum('valueEth').alias('total_value'),
                avg('valueEth').alias('avg_value'),
                min('valueEth').alias('min_value'),
                max('valueEth').alias('max_value')
            ).collect()[0]
            
            # 网络拓扑统计
            edge_stats = df.groupBy('fromAddr', 'toAddr').count()
            avg_edges_per_pair = edge_stats.agg(avg('count')).collect()[0][0]
            max_edges_per_pair = edge_stats.agg(max('count')).collect()[0][0]
            
            # 节点度数分析
            out_degree = df.groupBy('fromAddr').count().withColumnRenamed('count', 'out_degree')
            in_degree = df.groupBy('toAddr').count().withColumnRenamed('count', 'in_degree')
            
            avg_out_degree = out_degree.agg(avg('out_degree')).collect()[0][0]
            avg_in_degree = in_degree.agg(avg('in_degree')).collect()[0][0]
            max_out_degree = out_degree.agg(max('out_degree')).collect()[0][0]
            max_in_degree = in_degree.agg(max('in_degree')).collect()[0][0]
            
            # 时间分析
            time_stats = df.agg(
                min('timestamp').alias('min_timestamp'),
                max('timestamp').alias('max_timestamp')
            ).collect()[0]
            
            return {
                'analysis_metadata': {
                    'start_time': start_time.isoformat(),
                    'end_time': end_time.isoformat(),
                    'analysis_timestamp': datetime.now().isoformat()
                },
                'basic_stats': {
                    'total_transactions': total_count,
                    'unique_from_addresses': unique_from_addrs,
                    'unique_to_addresses': unique_to_addrs,
                    'unique_addresses': unique_addrs,
                    'address_reuse_ratio': round((unique_from_addrs + unique_to_addrs) / unique_addrs, 2) if unique_addrs > 0 else 0
                },
                'value_statistics': {
                    'total_value': float(value_stats['total_value']) if value_stats['total_value'] else 0,
                    'average_value': float(value_stats['avg_value']) if value_stats['avg_value'] else 0,
                    'min_value': float(value_stats['min_value']) if value_stats['min_value'] else 0,
                    'max_value': float(value_stats['max_value']) if value_stats['max_value'] else 0
                },
                'network_topology': {
                    'average_edges_per_pair': float(avg_edges_per_pair) if avg_edges_per_pair else 0,
                    'max_edges_per_pair': int(max_edges_per_pair) if max_edges_per_pair else 0,
                    'average_out_degree': float(avg_out_degree) if avg_out_degree else 0,
                    'average_in_degree': float(avg_in_degree) if avg_in_degree else 0,
                    'max_out_degree': int(max_out_degree) if max_out_degree else 0,
                    'max_in_degree': int(max_in_degree) if max_in_degree else 0
                },
                'time_range': {
                    'data_start': datetime.fromtimestamp(time_stats['min_timestamp']).isoformat() if time_stats['min_timestamp'] else None,
                    'data_end': datetime.fromtimestamp(time_stats['max_timestamp']).isoformat() if time_stats['max_timestamp'] else None
                }
            }
            
        except Exception as e:
            logger.error(f"计算图统计信息失败: {e}")
            return {"error": f"Statistics calculation failed: {str(e)}"}

def main():
    """主函数 - 增强版本"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Neo4j Spark Connector AML Pipeline')
    parser.add_argument('--mode', choices=['stream', 'batch', 'debug'], 
                       default='stream', help='运行模式')
    parser.add_argument('--hours', type=int, default=24, 
                       help='批量分析回溯小时数')
    parser.add_argument('--debug', action='store_true', 
                       help='启用调试模式')
    
    args = parser.parse_args()
    
    connector = Neo4jSparkConnector()
    
    try:
        if args.mode == "batch":
            result = connector.run_batch_analysis(args.hours)
            print(json.dumps(result, indent=2, default=str))
        elif args.mode == "debug":
            connector.run_streaming_pipeline(debug_mode=True)
        else:
            connector.run_streaming_pipeline(debug_mode=args.debug)
            
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        raise

if __name__ == "__main__":
    main()