# 交易图可视化与呈现方案

## 🎯 当前状态
✅ **Spark成功构建交易图**：741节点/1811边，111节点/127边等多个图快照
✅ **图数据结构**：PyTorch Geometric格式，包含节点特征和边连接
✅ **实时流处理**：每30秒生成新的图快照

## 📊 图数据呈现方案

### 1. Neo4j浏览器可视化（推荐）
**状态**：Neo4j连接器问题待修复
**优势**：
- 专业的图数据库可视化界面
- 支持Cypher查询进行交互式探索
- 自动布局算法（力导向、层次结构等）
- 节点/边属性展示和过滤

**访问方式**：
```
URL: http://localhost:7474
用户名: neo4j
密码: blockchain-aml-2024
```

**示例查询**：
```cypher
// 查看所有地址节点
MATCH (a:Address) RETURN a LIMIT 50

// 查看高价值交易网络
MATCH (from:Address)-[tx:TRANSACTS_WITH]->(to:Address)
WHERE tx.valueEth > 1.0
RETURN from, tx, to LIMIT 100

// 查找可疑的交易模式
MATCH (a:Address)-[tx:TRANSACTS_WITH*2..3]-(b:Address)
WHERE a.address <> b.address
RETURN a, tx, b LIMIT 50
```

### 2. Web可视化界面（可实现）
**技术栈**：
- 前端：React + D3.js/Cytoscape.js
- 后端：FastAPI/Flask
- 数据源：从Spark图快照或Neo4j读取

**功能特性**：
- 实时图数据更新
- 交互式节点/边探索
- 异常检测结果高亮
- 时间序列图演化

### 3. Jupyter Notebook分析（当前可用）
**创建可视化脚本**：
```python
import networkx as nx
import matplotlib.pyplot as plt
import pandas as pd
from kafka import KafkaConsumer
import json

# 从Kafka读取图快照
def visualize_latest_graph():
    consumer = KafkaConsumer('graph-snapshots', 
                           bootstrap_servers=['localhost:9092'])
    
    for message in consumer:
        graph_data = json.loads(message.value)
        
        # 创建NetworkX图
        G = nx.Graph()
        
        # 添加节点和边
        for node in graph_data['nodes']:
            G.add_node(node['address'], **node['features'])
        
        for edge in graph_data['edges']:
            G.add_edge(edge['source'], edge['target'], 
                      weight=edge['value_eth'])
        
        # 可视化
        plt.figure(figsize=(15, 10))
        pos = nx.spring_layout(G, k=1, iterations=50)
        
        # 绘制节点（大小根据交易金额）
        node_sizes = [G.degree(node) * 100 for node in G.nodes()]
        nx.draw_networkx_nodes(G, pos, node_size=node_sizes, 
                              node_color='lightblue', alpha=0.7)
        
        # 绘制边（粗细根据交易金额）
        edge_weights = [G[u][v]['weight'] for u, v in G.edges()]
        nx.draw_networkx_edges(G, pos, width=edge_weights, 
                              alpha=0.5, edge_color='gray')
        
        plt.title(f"交易图快照 - {len(G.nodes())}节点, {len(G.edges())}边")
        plt.axis('off')
        plt.show()
        break
```

### 4. 图分析报告（当前输出）
**当前Spark输出包含**：
- 图快照统计：节点数、边数
- 窗口时间信息：开始/结束时间
- 处理性能指标：构建时间、批次ID

**增强输出示例**：
```json
{
  "graph_snapshot": {
    "num_nodes": 741,
    "num_edges": 1811,
    "window_start": "2025-08-09T05:26:00",
    "window_end": "2025-08-09T05:27:00",
    "top_addresses": [
      {"address": "0xA111...", "degree": 45, "total_value": 123.5},
      {"address": "0xB222...", "degree": 32, "total_value": 89.2}
    ],
    "suspicious_patterns": [
      {"pattern": "high_frequency", "count": 12},
      {"pattern": "unusual_amounts", "count": 3}
    ]
  }
}
```

## 🔧 实现建议

### 短期（立即可用）
1. **修复Neo4j连接器**：解决JAR加载问题
2. **增强日志输出**：添加更多图统计信息
3. **创建简单的图数据导出**：JSON/CSV格式

### 中期（1-2周）
1. **开发Web可视化界面**
2. **集成异常检测结果**
3. **添加实时监控面板**

### 长期（1个月+）
1. **完整的AML分析平台**
2. **机器学习模型集成**
3. **合规报告生成**

## 📈 当前可访问的图数据

1. **Spark日志**：实时图构建统计
2. **Kafka主题**：`graph-snapshots`（计划中）
3. **Neo4j数据库**：持久化图存储（待修复连接器）
4. **文件输出**：`/tmp/graph-snapshots/`（可配置）

## 🎨 可视化效果预期

- **节点大小**：根据交易频次或金额
- **边粗细**：根据交易金额
- **颜色编码**：
  - 正常地址：蓝色
  - 可疑地址：红色
  - 高风险地址：深红色
- **布局算法**：力导向布局突出聚类模式
