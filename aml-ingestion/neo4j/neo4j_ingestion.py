#!/usr/bin/env python3
"""
区块链AML Neo4j数据摄取管道
Real-Time Blockchain AML Neo4j Data Ingestion Pipeline

从Kafka CEP层接收过滤后的交易数据，转换为图结构存储到Neo4j
"""

import json
import logging
import os
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
import asyncio
from dataclasses import dataclass

from kafka import KafkaConsumer
from neo4j import GraphDatabase, Driver
from neo4j.exceptions import Neo4jError
import pandas as pd

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class TransactionData:
    """交易数据结构"""
    txId: str
    fromAddr: str
    toAddr: Optional[str]
    valueEth: float
    gasPrice: int
    gasUsed: int
    blockNumber: int
    timestamp: int
    
    @classmethod
    def from_kafka_message(cls, message_data: Dict) -> 'TransactionData':
        """从Kafka消息创建TransactionData对象 - 期望camelCase字段"""
        try:
            # Validate required fields (camelCase from Flink CEP output)
            required_fields = ['txId', 'fromAddr', 'valueEth', 'gasPrice', 'gasUsed', 'blockNumber', 'timestamp']
            missing_fields = [field for field in required_fields if field not in message_data]
            if missing_fields:
                raise ValueError(f"Missing required fields: {missing_fields}")
            
            toAddr = message_data.get('toAddr') 
            if toAddr == "" or toAddr is None:
                toAddr = None

            return cls(
                txId=message_data['txId'],
                fromAddr=message_data['fromAddr'],
                toAddr=toAddr,
                valueEth=float(message_data['valueEth']),
                gasPrice=int(message_data['gasPrice']),
                gasUsed=int(message_data['gasUsed']),
                blockNumber=int(message_data['blockNumber']),
                timestamp=int(message_data['timestamp'])
            )
        except (ValueError, KeyError, TypeError) as e:
            logger.error(f"Error parsing Kafka message: {e}")
            logger.error(f"Message data: {message_data}")
            raise

class Neo4jIngestionPipeline:
    """Neo4j数据摄取管道"""
    
    def __init__(self):
        # Kafka配置
        self.kafka_config = {
            'bootstrap_servers': os.getenv('KAFKA_BOOTSTRAP_SERVERS', 'kafka:29092'),
            'group_id': 'neo4j-ingestion-group',
            'auto_offset_reset': 'latest',
            'enable_auto_commit': True,
            'value_deserializer': self._safe_json_deserializer
        }
        
        # Neo4j配置
        self.neo4j_uri = os.getenv('NEO4J_URI', 'bolt://neo4j:7687')
        self.neo4j_user = os.getenv('NEO4J_USER', 'neo4j')
        self.neo4j_password = os.getenv('NEO4J_PASSWORD', 'blockchain-aml-2024')
        
        # 主题配置
        self.input_topic = 'filtered-transactions'
        
        # 性能配置
        self.batch_size = 100
        self.batch_timeout = 5  # 秒
        
        # 初始化连接
        self.driver: Optional[Driver] = None
        self.consumer: Optional[KafkaConsumer] = None
        
        # 统计信息
        self.stats = {
            'processed_transactions': 0,
            'created_addresses': 0,
            'updated_addresses': 0,
            'errors': 0,
            'json_parse_errors': 0,
            'start_time': time.time()
        }
    
    def _safe_json_deserializer(self, value: bytes) -> Dict:
        """安全的JSON反序列化器，增强错误处理
           Safe JSON deserializer with enhanced error handling
        """
        try:
            if not value:
                logger.warning("Received empty message")
                return {}
            
            #解码字节数据
            json_str = value.decode('utf-8')
            logger.debug(f"Decoded JSON string: {json_str}")

            #检查JSON字符串长度和格式
            if len(json_str) < 10:
                logger.warning(f"JSON string too short, {json_str}")
                return {}
            
            #修复双引号问题然后解析JSON
            # 修复 "toAddr":""value"" -> "toAddr":"value"
            fixed_json_str = json_str.replace('":""', '":"').replace('"",', '",')
            parsed_data = json.loads(fixed_json_str)

            #验证解析结果是否为字典
            if not isinstance(parsed_data, dict):
                logger.warning(f"Invalid JSON format: {type(parsed_data)}")
                return {}
            
            logger.debug(f"Parsed JSON data: {parsed_data}")
            return parsed_data
        
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析错误: {e}")
            logger.error(f"错误位置: line {e.lineno}, column {e.colno}")
            logger.error(f"原始数据: {value}")
            logger.error(f"解码后的字符串: {value.decode('utf-8', errors='replace')}")
            self.stats['json_parse_errors'] += 1
            return {}
        except UnicodeDecodeError as e:
            logger.error(f"UTF-8解码错误: {e}")
            logger.error(f"原始数据: {value}")
            self.stats['json_parse_errors'] += 1
            return {}
        except Exception as e:
            logger.error(f"JSON反序列化未知错误: {e}")
            logger.error(f"原始数据: {value}")
            self.stats['json_parse_errors'] += 1
            return {}

    def connect_neo4j(self) -> bool:
        """连接Neo4j数据库"""
        try:
            self.driver = GraphDatabase.driver(
                self.neo4j_uri,
                auth=(self.neo4j_user, self.neo4j_password)
            )
            
            # 测试连接
            with self.driver.session() as session:
                result = session.run("RETURN 1 as test")
                result.single()
                
            logger.info(f"成功连接到Neo4j: {self.neo4j_uri}")
            return True
            
        except Exception as e:
            logger.error(f"连接Neo4j失败: {e}")
            return False
    
    def connect_kafka(self) -> bool:
        """连接Kafka消费者"""
        try:
            self.consumer = KafkaConsumer(
                self.input_topic,
                **self.kafka_config
            )
            logger.info(f"成功连接到Kafka topic: {self.input_topic}")
            return True
            
        except Exception as e:
            logger.error(f"连接Kafka失败: {e}")
            return False
    
    def validate_transaction_data(self, tx_data: Dict) -> bool:
        """验证交易数据的完整性 - 期望camelCase字段"""
        required_fields = ['txId', 'fromAddr', 'valueEth', 'blockNumber', 'timestamp', 'gasPrice', 'gasUsed']
        
        for field in required_fields:
            if field not in tx_data:
                logger.warning(f"交易数据缺少必需字段: {field}")
                return False
        
        # 验证数据类型
        try:
            float(tx_data['valueEth'])
            int(tx_data['blockNumber'])
            int(tx_data['timestamp'])
            int(tx_data['gasPrice'])
            int(tx_data['gasUsed'])
        except (ValueError, TypeError):
            logger.warning(f"交易数据类型错误: {tx_data}")
            return False
        
        return True

    def create_or_update_address(self, session, address: str, 
                               is_sender: bool = False, 
                               transaction_data: Optional[TransactionData] = None) -> None:
        """创建或更新地址节点"""
        try:
            # 基本的地址创建/更新查询
            query = """
            MERGE (a:Address {address: $address})
            ON CREATE SET 
                a.balance = 0.0,
                a.total_received = 0.0,
                a.total_sent = 0.0,
                a.transaction_count = 0,
                a.first_seen = $timestamp,
                a.last_seen = $timestamp,
                a.risk_score = 0,
                a.risk_level = 'LOW',
                a.is_contract = false,
                a.labels = []
            ON MATCH SET
                a.last_seen = $timestamp,
                a.transaction_count = a.transaction_count + 1
            """
            
            if transaction_data:
                if is_sender:
                    query += """
                    ON MATCH SET
                        a.total_sent = a.total_sent + $value,
                        a.balance = a.balance - $value
                    """
                else:
                    query += """
                    ON MATCH SET
                        a.total_received = a.total_received + $value,
                        a.balance = a.balance + $value
                    """
            
            params = {
                'address': address,
                'timestamp': transaction_data.timestamp if transaction_data else int(time.time()),
                'value': transaction_data.valueEth if transaction_data else 0.0
            }
            
            session.run(query, params)
            
        except Exception as e:
            logger.error(f"创建/更新地址节点失败 {address}: {e}")
            raise
    
    def create_transaction_node(self, session, tx_data: TransactionData) -> None:
        """创建交易节点"""
        try:
            query = """
            CREATE (t:Transaction {
                txId: $txId,
                fromAddr: $fromAddr,
                toAddr: $toAddr,
                valueEth: $valueEth,
                gasPrice: $gasPrice,
                gasUsed: $gasUsed,
                blockNumber: $blockNumber,
                timestamp: $timestamp,
                status: 1,
                is_suspicious: false,
                suspicion_reasons: []
            })
            """
            
            params = {
                'txId': tx_data.txId,
                'fromAddr': tx_data.fromAddr,
                'toAddr': tx_data.toAddr,
                'valueEth': tx_data.valueEth,
                'gasPrice': tx_data.gasPrice,
                'gasUsed': tx_data.gasUsed,
                'blockNumber': tx_data.blockNumber,
                'timestamp': tx_data.timestamp
            }
            
            session.run(query, params)
            
        except Exception as e:
            logger.error(f"创建交易节点失败 {tx_data.txId}: {e}")
            raise
    
    def create_relationships(self, session, tx_data: TransactionData) -> None:
        """创建交易关系"""
        try:
            # 创建SENDS关系
            sends_query = """
            MATCH (a:Address {address: $fromAddr})
            MATCH (t:Transaction {txId: $txId})
            CREATE (a)-[:SENDS {
                valueEth: $valueEth,
                timestamp: $timestamp,
                gasFee: $gasFee
            }]->(t)
            """

            gasFee = (tx_data.gasPrice * tx_data.gasUsed) / 1e18  # 转换为ETH
            
            session.run(sends_query, {
                'fromAddr': tx_data.fromAddr,
                'txId': tx_data.txId,
                'valueEth': tx_data.valueEth,
                'timestamp': tx_data.timestamp,
                'gasFee': gasFee
            })
            
            # 如果有接收方，创建RECEIVES关系
            if tx_data.toAddr:
                receives_query = """
                MATCH (t:Transaction {txId: $txId})
                MATCH (a:Address {address: $toAddr})
                CREATE (t)-[:RECEIVES {
                    valueEth: $valueEth,
                    timestamp: $timestamp
                }]->(a)
                """
                
                session.run(receives_query, {
                    'txId': tx_data.txId,
                    'toAddr': tx_data.toAddr,
                    'valueEth': tx_data.valueEth,
                    'timestamp': tx_data.timestamp
                })
            
            # 创建或更新TRANSACTS_WITH关系
            if tx_data.toAddr:
                transacts_query = """
                MATCH (from:Address {address: $fromAddr})
                MATCH (to:Address {address: $toAddr})
                MERGE (from)-[r:TRANSACTS_WITH]->(to)
                ON CREATE SET
                    r.totalValue = $valueEth,
                    r.transactionCount = 1,
                    r.firstTransaction = $timestamp,
                    r.lastTransaction = $timestamp,
                    r.avgValue = $valueEth,
                    r.riskLevel = 'LOW'
                ON MATCH SET
                    r.totalValue = r.totalValue + $valueEth,
                    r.transactionCount = r.transactionCount + 1,
                    r.lastTransaction = $timestamp,
                    r.avgValue = r.totalValue / r.transactionCount
                """
                
                session.run(transacts_query, {
                    'fromAddr': tx_data.fromAddr,
                    'toAddr': tx_data.toAddr,
                    'valueEth': tx_data.valueEth,
                    'timestamp': tx_data.timestamp
                })
                
        except Exception as e:
            logger.error(f"创建关系失败 {tx_data.txId}: {e}")
            raise
    
    def process_transaction_batch(self, transactions: List[TransactionData]) -> None:
        """批量处理交易数据"""
        if not transactions:
            return
            
        try:
            with self.driver.session() as session:
                with session.begin_transaction() as tx:
                    for tx_data in transactions:
                        try:
                            # 1. 创建或更新发送方地址
                            self.create_or_update_address(
                                tx, tx_data.fromAddr, is_sender=True, 
                                transaction_data=tx_data
                            )
                            
                            # 2. 创建或更新接收方地址（如果存在）
                            if tx_data.toAddr:
                                self.create_or_update_address(
                                    tx, tx_data.toAddr, is_sender=False,
                                    transaction_data=tx_data
                                )
                            
                            # 3. 创建交易节点
                            self.create_transaction_node(tx, tx_data)
                            
                            # 4. 创建关系
                            self.create_relationships(tx, tx_data)
                            
                            self.stats['processed_transactions'] += 1
                            
                        except Exception as e:
                            logger.error(f"处理交易失败 {tx_data.txId}: {e}")
                            self.stats['errors'] += 1
                            continue
                    
                    # 提交事务
                    tx.commit()
                    
            logger.info(f"成功处理批次: {len(transactions)} 笔交易")
            
        except Exception as e:
            logger.error(f"批量处理交易失败: {e}")
            self.stats['errors'] += len(transactions)
    
    def print_stats(self) -> None:
        """打印统计信息"""
        uptime = time.time() - self.stats['start_time']
        tps = self.stats['processed_transactions'] / uptime if uptime > 0 else 0
        
        logger.info(f"""
        === Neo4j摄取管道统计 ===
        运行时间: {uptime:.1f}秒
        已处理交易: {self.stats['processed_transactions']}
        创建地址: {self.stats['created_addresses']}
        更新地址: {self.stats['updated_addresses']}
        错误数量: {self.stats['errors']}
        处理速度: {tps:.2f} TPS
        """)
    
    def debug_kafka_messages(self, max_messages: int = 5) -> None:
        """调试Kafka消息内容"""
        logger.info(f"开始调试Kafka消息，检查前{max_messages}条消息...")
        
        try:
            # 创建临时消费者用于调试
            debug_consumer = KafkaConsumer(
                self.input_topic,
                bootstrap_servers=self.kafka_config['bootstrap_servers'],
                group_id='debug-group-' + str(int(time.time())),
                auto_offset_reset='earliest',
                value_deserializer=lambda x: x  # 不进行JSON解析，直接返回字节
            )
            
            message_count = 0
            for message in debug_consumer:
                message_count += 1
                logger.info(f"=== 调试消息 {message_count} ===")
                logger.info(f"原始字节数据: {message.value}")
                
                try:
                    decoded = message.value.decode('utf-8')
                    logger.info(f"UTF-8解码结果: {decoded}")
                    
                    # 尝试JSON解析
                    parsed = json.loads(decoded)
                    logger.info(f"JSON解析成功: {parsed}")
                except Exception as e:
                    logger.error(f"解析失败: {e}")
                
                if message_count >= max_messages:
                    break
            
            debug_consumer.close()
            logger.info("调试完成")
            
        except Exception as e:
            logger.error(f"调试过程中出错: {e}")

    def run(self) -> None:
        """运行数据摄取管道"""
        logger.info("启动Neo4j数据摄取管道...")
        
        # 连接数据库
        if not self.connect_neo4j():
            logger.error("无法连接Neo4j，退出")
            return
            
        if not self.connect_kafka():
            logger.error("无法连接Kafka，退出")
            return
        
        logger.info("开始消费交易数据...")
        
        batch = []
        last_batch_time = time.time()
        
        try:
            for message in self.consumer:
                try:
                    # 解析交易数据
                    tx_data = TransactionData.from_kafka_message(message.value)
                    batch.append(tx_data)
                    
                    # 检查是否需要处理批次
                    current_time = time.time()
                    should_process = (
                        len(batch) >= self.batch_size or 
                        (current_time - last_batch_time) >= self.batch_timeout
                    )
                    
                    if should_process and batch:
                        self.process_transaction_batch(batch)
                        batch = []
                        last_batch_time = current_time
                        
                        # 定期打印统计信息
                        if self.stats['processed_transactions'] % 1000 == 0:
                            self.print_stats()
                    
                except Exception as e:
                    logger.error(f"处理消息失败: {e}")
                    continue
                    
        except KeyboardInterrupt:
            logger.info("收到停止信号...")
        except Exception as e:
            logger.error(f"管道运行错误: {e}")
        finally:
            # 处理剩余批次
            if batch:
                self.process_transaction_batch(batch)
            
            # 关闭连接
            if self.consumer:
                self.consumer.close()
            if self.driver:
                self.driver.close()
                
            self.print_stats()
            logger.info("Neo4j数据摄取管道已停止")

def main():
    """主函数"""
    pipeline = Neo4jIngestionPipeline()
    pipeline.run()

if __name__ == "__main__":
    main()