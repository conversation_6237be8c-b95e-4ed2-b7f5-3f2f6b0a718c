#!/usr/bin/env python3
"""
Neo4j Schema Validation and Initialization
==========================================
Validates and initializes the blockchain AML graph schema in Neo4j.
"""

import logging
import os
import time
from typing import Dict, List, Optional, Tuple
from datetime import datetime

from neo4j import GraphDatabase, basic_auth
from neo4j.exceptions import ServiceUnavailable, AuthError

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class Neo4jSchemaValidator:
    """Validates and initializes Neo4j schema for blockchain AML."""
    
    def __init__(self, uri: str = "bolt://localhost:7687", 
                 username: str = "neo4j", 
                 password: str = "blockchain-aml-2024"):
        """Initialize Neo4j connection."""
        self.uri = uri
        self.username = username
        self.password = password
        self.driver = None
        
    def connect(self) -> bool:
        """Connect to Neo4j database with retry logic."""
        max_retries = 5
        retry_delay = 5
        
        for attempt in range(max_retries):
            try:
                logger.info(f"🔌 Attempting to connect to Neo4j at {self.uri} (attempt {attempt + 1}/{max_retries})")
                
                self.driver = GraphDatabase.driver(
                    self.uri, 
                    auth=basic_auth(self.username, self.password)
                )
                
                # Test connection
                with self.driver.session() as session:
                    result = session.run("RETURN 1 AS test")
                    test_value = result.single()["test"]
                    
                if test_value == 1:
                    logger.info("✅ Successfully connected to Neo4j")
                    return True
                    
            except (ServiceUnavailable, AuthError) as e:
                logger.warning(f"⚠️ Connection attempt {attempt + 1} failed: {e}")
                if attempt < max_retries - 1:
                    logger.info(f"⏳ Retrying in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                else:
                    logger.error("❌ Failed to connect to Neo4j after all retries")
                    return False
                    
        return False
    
    def close(self):
        """Close Neo4j connection."""
        if self.driver:
            self.driver.close()
            logger.info("🔌 Neo4j connection closed")
    
    def run_cypher_file(self, filepath: str) -> bool:
        """Execute Cypher commands from file."""
        try:
            with open(filepath, 'r') as file:
                cypher_content = file.read()
            
            # Split by semicolon and filter out comments/empty lines
            commands = [
                cmd.strip() 
                for cmd in cypher_content.split(';') 
                if cmd.strip() and not cmd.strip().startswith('//')
            ]
            
            logger.info(f"📋 Executing {len(commands)} Cypher commands from {filepath}")
            
            with self.driver.session() as session:
                for i, command in enumerate(commands, 1):
                    if command:
                        try:
                            session.run(command)
                            logger.debug(f"✅ Command {i}/{len(commands)} executed successfully")
                        except Exception as e:
                            logger.warning(f"⚠️ Command {i} failed (might be expected): {e}")
            
            logger.info(f"✅ Completed executing {filepath}")
            return True
            
        except FileNotFoundError:
            logger.error(f"❌ File not found: {filepath}")
            return False
        except Exception as e:
            logger.error(f"❌ Error executing {filepath}: {e}")
            return False
    
    def create_constraints_and_indexes(self) -> bool:
        """Create all constraints and indexes."""
        logger.info("🏗️ Creating constraints and indexes...")
        
        constraints_indexes = [
            # Unique constraints
            "CREATE CONSTRAINT tx_hash_unique IF NOT EXISTS FOR (tx:Transaction) REQUIRE tx.txId IS UNIQUE",
            "CREATE CONSTRAINT address_unique IF NOT EXISTS FOR (addr:Address) REQUIRE addr.address IS UNIQUE",
            "CREATE CONSTRAINT block_number_unique IF NOT EXISTS FOR (block:Block) REQUIRE block.blockNumber IS UNIQUE",
            "CREATE CONSTRAINT alert_id_unique IF NOT EXISTS FOR (alert:Alert) REQUIRE alert.alertId IS UNIQUE",
            
            # Existence constraints
            "CREATE CONSTRAINT tx_required_props IF NOT EXISTS FOR (tx:Transaction) REQUIRE tx.txId IS NOT NULL",
            "CREATE CONSTRAINT address_required IF NOT EXISTS FOR (addr:Address) REQUIRE addr.address IS NOT NULL",
            
            # Performance indexes
            "CREATE INDEX tx_timestamp_idx IF NOT EXISTS FOR (tx:Transaction) ON (tx.timestamp)",
            "CREATE INDEX tx_block_number_idx IF NOT EXISTS FOR (tx:Transaction) ON (tx.blockNumber)",
            "CREATE INDEX tx_anomaly_score_idx IF NOT EXISTS FOR (tx:Transaction) ON (tx.anomalyScore)",
            "CREATE INDEX tx_flagged_idx IF NOT EXISTS FOR (tx:Transaction) ON (tx.isFlagged)",
            "CREATE INDEX addr_risk_score_idx IF NOT EXISTS FOR (addr:Address) ON (addr.riskScore)",
            "CREATE INDEX addr_suspicious_idx IF NOT EXISTS FOR (addr:Address) ON (addr.isSuspicious)",
            "CREATE INDEX tx_value_idx IF NOT EXISTS FOR (tx:Transaction) ON (tx.valueEth)",
            
            # Relationship indexes
            "CREATE INDEX transfers_timestamp_idx IF NOT EXISTS FOR ()-[r:TRANSFERS]-() ON (r.timestamp)",
            "CREATE INDEX sends_value_idx IF NOT EXISTS FOR ()-[r:SENDS]-() ON (r.valueEth)",
        ]
        
        success_count = 0
        with self.driver.session() as session:
            for i, command in enumerate(constraints_indexes, 1):
                try:
                    session.run(command)
                    success_count += 1
                    logger.debug(f"✅ Constraint/Index {i}/{len(constraints_indexes)} created")
                except Exception as e:
                    # Many constraints/indexes might already exist
                    logger.debug(f"⚠️ Constraint/Index {i} creation message: {e}")
        
        logger.info(f"✅ Processed {success_count}/{len(constraints_indexes)} constraints/indexes")
        return True
    
    def validate_schema(self) -> Dict[str, bool]:
        """Validate that schema is properly set up."""
        logger.info("🔍 Validating Neo4j schema...")
        
        validation_results = {}
        
        with self.driver.session() as session:
            # Check constraints
            try:
                result = session.run("SHOW CONSTRAINTS")
                constraints = [record["name"] for record in result]
                validation_results["constraints"] = len(constraints) > 0
                logger.info(f"✅ Found {len(constraints)} constraints")
            except Exception as e:
                validation_results["constraints"] = False
                logger.error(f"❌ Error checking constraints: {e}")
            
            # Check indexes
            try:
                result = session.run("SHOW INDEXES")
                indexes = [record["name"] for record in result]
                validation_results["indexes"] = len(indexes) > 0
                logger.info(f"✅ Found {len(indexes)} indexes")
            except Exception as e:
                validation_results["indexes"] = False
                logger.error(f"❌ Error checking indexes: {e}")
            
            # Test basic operations
            try:
                # Test node creation
                session.run("""
                    MERGE (test:TestNode {id: 'schema_validation_test'})
                    RETURN test.id AS testId
                """)
                
                # Test node retrieval
                result = session.run("""
                    MATCH (test:TestNode {id: 'schema_validation_test'})
                    RETURN count(test) AS count
                """)
                count = result.single()["count"]
                validation_results["basic_operations"] = count == 1
                
                # Cleanup test node
                session.run("MATCH (test:TestNode {id: 'schema_validation_test'}) DELETE test")
                
                logger.info("✅ Basic operations test passed")
                
            except Exception as e:
                validation_results["basic_operations"] = False
                logger.error(f"❌ Basic operations test failed: {e}")
        
        return validation_results
    
    def create_sample_data(self) -> bool:
        """Create sample data for testing."""
        logger.info("📊 Creating sample data for testing...")
        
        sample_data_queries = [
            # Create sample addresses
            """
            MERGE (addr1:Address {
                address: "******************************************",
                addressType: "wallet",
                firstSeen: datetime("2024-01-01T00:00:00Z"),
                lastActive: datetime("2024-08-05T12:00:00Z"),
                totalTxCount: 25,
                totalVolumeEth: 15.5,
                riskScore: 0.75,
                isSuspicious: true,
                labels: ["high_risk", "frequent_trader"]
            })
            """,
            
            """
            MERGE (addr2:Address {
                address: "******************************************", 
                addressType: "exchange",
                firstSeen: datetime("2023-06-01T00:00:00Z"),
                lastActive: datetime("2024-08-05T11:30:00Z"),
                totalTxCount: 500,
                totalVolumeEth: 1000.0,
                riskScore: 0.1,
                isSuspicious: false,
                labels: ["exchange", "verified"]
            })
            """,
            
            # Create sample transaction
            """
            MERGE (tx1:Transaction {
                txId: "0xsample123def456789abcdef0123456789abcdef0123456789abcdef01",
                blockNumber: 18500000,
                timestamp: datetime("2024-08-05T12:30:45Z"),
                valueEth: 5.0,
                gasUsed: 21000,
                gasPrice: 20000000000,
                txFeeEth: 0.00042,
                nonce: 15,
                status: "success",
                anomalyScore: 0.85,
                isFlagged: true,
                cepFiltered: true
            })
            """,
            
            # Create relationships
            """
            MATCH (addr1:Address {address: "******************************************"})
            MATCH (addr2:Address {address: "******************************************"})
            MATCH (tx1:Transaction {txId: "0xsample123def456789abcdef0123456789abcdef0123456789abcdef01"})
            
            MERGE (addr1)-[:SENDS {
                timestamp: datetime("2024-08-05T12:30:45Z"),
                valueEth: 5.0,
                txIndex: 1
            }]->(tx1)
            
            MERGE (tx1)-[:RECEIVES {
                timestamp: datetime("2024-08-05T12:30:45Z"),
                valueEth: 5.0
            }]->(addr2)
            
            MERGE (addr1)-[:TRANSFERS {
                timestamp: datetime("2024-08-05T12:30:45Z"),
                valueEth: 5.0,
                txId: "0xsample123def456789abcdef0123456789abcdef0123456789abcdef01",
                hopCount: 1
            }]->(addr2)
            """
        ]
        
        with self.driver.session() as session:
            for i, query in enumerate(sample_data_queries, 1):
                try:
                    session.run(query)
                    logger.debug(f"✅ Sample data query {i}/{len(sample_data_queries)} executed")
                except Exception as e:
                    logger.error(f"❌ Sample data query {i} failed: {e}")
                    return False
        
        logger.info("✅ Sample data created successfully")
        return True
    
    def get_database_stats(self) -> Dict[str, int]:
        """Get database statistics."""
        stats = {}
        
        with self.driver.session() as session:
            # Count nodes by label
            node_counts = session.run("""
                CALL db.labels() YIELD label
                CALL apoc.cypher.run('MATCH (n:' + label + ') RETURN count(*) AS count', {}) 
                YIELD value
                RETURN label, value.count AS count
            """)
            
            for record in node_counts:
                stats[f"{record['label']}_nodes"] = record["count"]
            
            # Count relationships
            rel_count = session.run("MATCH ()-[r]->() RETURN count(r) AS count").single()["count"]
            stats["total_relationships"] = rel_count
        
        return stats

def main():
    """Main function to initialize Neo4j schema."""
    logger.info("🚀 Starting Neo4j Schema Initialization")
    
    # Initialize validator
    validator = Neo4jSchemaValidator()
    
    try:
        # Connect to Neo4j
        if not validator.connect():
            logger.error("❌ Failed to connect to Neo4j. Exiting.")
            return False
        
        # Create constraints and indexes
        validator.create_constraints_and_indexes()
        
        # Validate schema
        validation_results = validator.validate_schema()
        
        if all(validation_results.values()):
            logger.info("✅ Schema validation passed!")
        else:
            logger.warning(f"⚠️ Schema validation issues: {validation_results}")
        
        # Create sample data for testing
        validator.create_sample_data()
        
        # Get database statistics
        stats = validator.get_database_stats()
        logger.info(f"📊 Database Statistics: {stats}")
        
        logger.info("🎉 Neo4j Schema Initialization Complete!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Schema initialization failed: {e}")
        return False
        
    finally:
        validator.close()

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)