// ===================================================================
// 区块链反洗钱 (AML) Cypher查询集合
// Blockchain Anti-Money Laundering (AML) Cypher Query Collection
// ===================================================================

// ===================
// 1. 基础统计查询
// ===================

// 1.1 数据库概览统计
MATCH (a:Address) 
WITH count(a) as address_count
MATCH (t:Transaction)
WITH address_count, count(t) as transaction_count
MATCH ()-[r]->()
RETURN 
    address_count,
    transaction_count,
    count(r) as relationship_count,
    datetime() as query_time;

// 1.2 交易量统计（按时间段）
MATCH (t:Transaction)
WHERE t.timestamp >= timestamp() - 86400000  // 最近24小时
RETURN 
    date(datetime({epochMillis: t.timestamp})) as date,
    count(t) as daily_transactions,
    sum(t.value_eth) as daily_volume,
    avg(t.value_eth) as avg_transaction_value
ORDER BY date DESC;

// 1.3 最活跃地址排名
MATCH (a:Address)
RETURN 
    a.address,
    a.transaction_count,
    a.total_sent,
    a.total_received,
    a.risk_score,
    a.risk_level
ORDER BY a.transaction_count DESC
LIMIT 20;

// ===================
// 2. 可疑活动检测查询
// ===================

// 2.1 大额交易检测（超过阈值的交易）
MATCH (t:Transaction)
WHERE t.value_eth > 100.0  // 100 ETH阈值
RETURN 
    t.tx_id,
    t.from_addr,
    t.to_addr,
    t.value_eth,
    datetime({epochMillis: t.timestamp}) as transaction_time,
    t.block_number
ORDER BY t.value_eth DESC
LIMIT 50;

// 2.2 高频交易检测（短时间内大量交易）
MATCH (a:Address)-[s:SENDS]->(t:Transaction)
WHERE t.timestamp >= timestamp() - 3600000  // 最近1小时
WITH a, count(t) as hourly_transactions, sum(t.value_eth) as hourly_volume
WHERE hourly_transactions > 50  // 每小时超过50笔交易
RETURN 
    a.address,
    hourly_transactions,
    hourly_volume,
    a.risk_score
ORDER BY hourly_transactions DESC;

// 2.3 圆整金额交易检测（可能的洗钱指标）
MATCH (t:Transaction)
WHERE t.value_eth = round(t.value_eth) AND t.value_eth >= 10.0
RETURN 
    t.tx_id,
    t.from_addr,
    t.to_addr,
    t.value_eth,
    datetime({epochMillis: t.timestamp}) as transaction_time
ORDER BY t.value_eth DESC
LIMIT 100;

// 2.4 快速连续交易检测
MATCH (a:Address)-[s1:SENDS]->(t1:Transaction),
      (a)-[s2:SENDS]->(t2:Transaction)
WHERE t1.timestamp < t2.timestamp 
  AND t2.timestamp - t1.timestamp < 60000  // 1分钟内
  AND t1.value_eth > 10.0
RETURN 
    a.address,
    t1.tx_id as first_tx,
    t2.tx_id as second_tx,
    t1.value_eth as first_value,
    t2.value_eth as second_value,
    (t2.timestamp - t1.timestamp) / 1000.0 as time_diff_seconds
ORDER BY time_diff_seconds ASC
LIMIT 50;

// ===================
// 3. 网络分析查询
// ===================

// 3.1 查找资金流路径（分层洗钱检测）
MATCH path = (start:Address)-[:TRANSACTS_WITH*2..5]->(end:Address)
WHERE start.address <> end.address
  AND ALL(r IN relationships(path) WHERE r.total_value > 1.0)
RETURN 
    start.address as source,
    end.address as destination,
    length(path) as path_length,
    reduce(total = 0, r IN relationships(path) | total + r.total_value) as total_flow,
    [node IN nodes(path) | node.address] as addresses_in_path
ORDER BY total_flow DESC
LIMIT 20;

// 3.2 识别可能的混币服务（中心化节点）
MATCH (a:Address)
WHERE a.transaction_count > 1000  // 高交易量
WITH a
MATCH (a)-[r:TRANSACTS_WITH]-(other:Address)
WITH a, count(DISTINCT other) as unique_counterparties, 
     avg(r.total_value) as avg_transaction_value
WHERE unique_counterparties > 100  // 与大量不同地址交易
RETURN 
    a.address,
    a.transaction_count,
    unique_counterparties,
    avg_transaction_value,
    a.risk_score
ORDER BY unique_counterparties DESC;

// 3.3 查找紧密连接的地址群组（可能的关联账户）
MATCH (a1:Address)-[r1:TRANSACTS_WITH]-(a2:Address)-[r2:TRANSACTS_WITH]-(a3:Address)
WHERE a1 <> a3 
  AND r1.transaction_count > 5 
  AND r2.transaction_count > 5
WITH a1, a2, a3, r1.total_value + r2.total_value as total_group_value
RETURN 
    [a1.address, a2.address, a3.address] as address_group,
    total_group_value,
    datetime() as analysis_time
ORDER BY total_group_value DESC
LIMIT 30;

// 3.4 检测异常交易模式（与历史行为不符）
MATCH (a:Address)-[r:TRANSACTS_WITH]->(target:Address)
WHERE r.last_transaction >= timestamp() - 86400000  // 最近24小时
WITH a, target, r, a.total_sent / a.transaction_count as historical_avg
WHERE r.avg_value > historical_avg * 10  // 平均交易额超过历史10倍
RETURN 
    a.address as sender,
    target.address as receiver,
    r.avg_value as recent_avg_value,
    historical_avg,
    r.avg_value / historical_avg as anomaly_ratio
ORDER BY anomaly_ratio DESC;

// ===================
// 4. 风险评估查询
// ===================

// 4.1 计算地址风险评分
MATCH (a:Address)
OPTIONAL MATCH (a)-[r:TRANSACTS_WITH]-(risky:Address)
WHERE risky.risk_level IN ['HIGH', 'CRITICAL']
WITH a, count(risky) as risky_connections,
     CASE 
         WHEN a.total_sent > 1000 THEN 20
         WHEN a.total_sent > 100 THEN 10
         ELSE 0
     END as volume_risk,
     CASE 
         WHEN a.transaction_count > 1000 THEN 15
         WHEN a.transaction_count > 100 THEN 5
         ELSE 0
     END as frequency_risk
SET a.risk_score = volume_risk + frequency_risk + (risky_connections * 5)
RETURN a.address, a.risk_score;

// 4.2 更新风险等级
MATCH (a:Address)
SET a.risk_level = 
    CASE 
        WHEN a.risk_score >= 80 THEN 'CRITICAL'
        WHEN a.risk_score >= 60 THEN 'HIGH'
        WHEN a.risk_score >= 30 THEN 'MEDIUM'
        ELSE 'LOW'
    END
RETURN a.address, a.risk_score, a.risk_level;

// 4.3 识别高风险交易对
MATCH (a1:Address)-[r:TRANSACTS_WITH]->(a2:Address)
WHERE (a1.risk_level IN ['HIGH', 'CRITICAL'] OR a2.risk_level IN ['HIGH', 'CRITICAL'])
  AND r.total_value > 50.0
RETURN 
    a1.address as sender,
    a2.address as receiver,
    a1.risk_level as sender_risk,
    a2.risk_level as receiver_risk,
    r.total_value,
    r.transaction_count,
    datetime({epochMillis: r.last_transaction}) as last_transaction_time
ORDER BY r.total_value DESC;

// ===================
// 5. 合规报告查询
// ===================

// 5.1 生成日度AML报告
MATCH (t:Transaction)
WHERE t.timestamp >= timestamp() - 86400000
WITH date(datetime({epochMillis: t.timestamp})) as report_date
MATCH (suspicious:Transaction)
WHERE suspicious.is_suspicious = true
  AND date(datetime({epochMillis: suspicious.timestamp})) = report_date
WITH report_date, count(suspicious) as suspicious_count
MATCH (all_tx:Transaction)
WHERE date(datetime({epochMillis: all_tx.timestamp})) = report_date
RETURN 
    report_date,
    count(all_tx) as total_transactions,
    suspicious_count,
    round(suspicious_count * 100.0 / count(all_tx), 2) as suspicious_percentage,
    sum(all_tx.value_eth) as total_volume;

// 5.2 高风险地址监控列表
MATCH (a:Address)
WHERE a.risk_level IN ['HIGH', 'CRITICAL']
OPTIONAL MATCH (a)-[r:TRANSACTS_WITH]-(connected:Address)
RETURN 
    a.address,
    a.risk_level,
    a.risk_score,
    a.total_sent + a.total_received as total_activity,
    count(DISTINCT connected) as connected_addresses,
    datetime({epochMillis: a.last_seen}) as last_activity
ORDER BY a.risk_score DESC;

// 5.3 交易所地址活动报告（如果有标记）
MATCH (exchange:Address)
WHERE 'exchange' IN exchange.labels
OPTIONAL MATCH (exchange)-[r:TRANSACTS_WITH]-(other:Address)
WHERE r.last_transaction >= timestamp() - 86400000  // 最近24小时
RETURN 
    exchange.address,
    count(DISTINCT other) as daily_counterparties,
    sum(r.total_value) as daily_volume,
    avg(r.avg_value) as avg_transaction_size
ORDER BY daily_volume DESC;

// ===================
// 6. 图可视化查询
// ===================

// 6.1 可疑地址及其直接连接的可视化
MATCH (center:Address)
WHERE center.risk_level IN ['HIGH', 'CRITICAL']
OPTIONAL MATCH (center)-[r:TRANSACTS_WITH]-(connected:Address)
WHERE r.total_value > 10.0
RETURN center, r, connected
LIMIT 200;

// 6.2 大额资金流网络可视化
MATCH (a:Address)-[r:TRANSACTS_WITH]->(b:Address)
WHERE r.total_value > 100.0
RETURN a, r, b
ORDER BY r.total_value DESC
LIMIT 100;

// 6.3 时间序列交易活动
MATCH (t:Transaction)
WHERE t.timestamp >= timestamp() - 604800000  // 最近7天
RETURN 
    date(datetime({epochMillis: t.timestamp})) as date,
    hour(datetime({epochMillis: t.timestamp})) as hour,
    count(t) as transaction_count,
    sum(t.value_eth) as hourly_volume
ORDER BY date, hour;

// ===================
// 7. 性能优化查询
// ===================

// 7.1 定期清理旧数据（可选）
// MATCH (t:Transaction)
// WHERE t.timestamp < timestamp() - 2592000000  // 30天前
// DETACH DELETE t;

// 7.2 重建统计信息
CALL db.stats.collect();

// 7.3 检查索引使用情况
CALL db.indexes() YIELD name, state, populationPercent
WHERE state = 'ONLINE'
RETURN name, populationPercent
ORDER BY populationPercent DESC;

// ===================
// 8. 实时监控查询
// ===================

// 8.1 最近1小时的异常活动
MATCH (t:Transaction)
WHERE t.timestamp >= timestamp() - 3600000
  AND (t.value_eth > 50.0 OR t.is_suspicious = true)
RETURN 
    t.tx_id,
    t.from_addr,
    t.to_addr,
    t.value_eth,
    t.is_suspicious,
    t.suspicion_reasons,
    datetime({epochMillis: t.timestamp}) as transaction_time
ORDER BY t.timestamp DESC
LIMIT 50;

// 8.2 实时风险地址活动监控
MATCH (risky:Address)-[s:SENDS]->(t:Transaction)
WHERE risky.risk_level IN ['HIGH', 'CRITICAL']
  AND t.timestamp >= timestamp() - 1800000  // 最近30分钟
RETURN 
    risky.address,
    risky.risk_level,
    count(t) as recent_transactions,
    sum(t.value_eth) as recent_volume,
    max(datetime({epochMillis: t.timestamp})) as latest_activity
ORDER BY recent_volume DESC;

RETURN "AML Cypher查询集合加载完成！" as status;