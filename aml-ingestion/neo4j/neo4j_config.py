#!/usr/bin/env python3
"""
Neo4j配置管理
Neo4j Configuration Management for AML Pipeline
"""

import os
import logging
from typing import Dict, Optional, Any
from dataclasses import dataclass
from neo4j import GraphDatabase, Driver
from neo4j.exceptions import Neo4jError

logger = logging.getLogger(__name__)

@dataclass
class Neo4jConfig:
    """Neo4j配置类"""
    uri: str = "bolt://neo4j:7687"
    user: str = "neo4j"
    password: str = "blockchain-aml-2024"
    database: str = "neo4j"
    encrypted: bool = False
    trust: str = "TRUST_ALL_CERTIFICATES"
    max_connection_lifetime: int = 3600
    max_connection_pool_size: int = 50
    connection_acquisition_timeout: int = 60
    
    @classmethod
    def from_env(cls) -> 'Neo4jConfig':
        """从环境变量创建配置"""
        return cls(
            uri=os.getenv('NEO4J_URI', cls.uri),
            user=os.getenv('NEO4J_USER', cls.user),
            password=os.getenv('NEO4J_PASSWORD', cls.password),
            database=os.getenv('NEO4J_DATABASE', cls.database),
            encrypted=os.getenv('NEO4J_ENCRYPTED', 'false').lower() == 'true'
        )

class Neo4jConnectionManager:
    """Neo4j连接管理器"""
    
    def __init__(self, config: Optional[Neo4jConfig] = None):
        self.config = config or Neo4jConfig.from_env()
        self.driver: Optional[Driver] = None
        self._connected = False
    
    def connect(self) -> bool:
        """建立Neo4j连接"""
        try:
            self.driver = GraphDatabase.driver(
                self.config.uri,
                auth=(self.config.user, self.config.password),
                encrypted=self.config.encrypted,
                trust=self.config.trust,
                max_connection_lifetime=self.config.max_connection_lifetime,
                max_connection_pool_size=self.config.max_connection_pool_size,
                connection_acquisition_timeout=self.config.connection_acquisition_timeout
            )
            
            # 测试连接
            with self.driver.session(database=self.config.database) as session:
                result = session.run("RETURN 1 as test")
                result.single()
            
            self._connected = True
            logger.info(f"成功连接到Neo4j: {self.config.uri}")
            return True
            
        except Exception as e:
            logger.error(f"连接Neo4j失败: {e}")
            self._connected = False
            return False
    
    def disconnect(self) -> None:
        """关闭Neo4j连接"""
        if self.driver:
            self.driver.close()
            self._connected = False
            logger.info("Neo4j连接已关闭")
    
    def is_connected(self) -> bool:
        """检查连接状态"""
        return self._connected and self.driver is not None
    
    def execute_query(self, query: str, parameters: Optional[Dict[str, Any]] = None) -> list:
        """执行Cypher查询"""
        if not self.is_connected():
            raise RuntimeError("Neo4j未连接")
        
        try:
            with self.driver.session(database=self.config.database) as session:
                result = session.run(query, parameters or {})
                return [record.data() for record in result]
                
        except Neo4jError as e:
            logger.error(f"执行查询失败: {e}")
            raise
    
    def execute_write_transaction(self, query: str, parameters: Optional[Dict[str, Any]] = None) -> list:
        """执行写事务"""
        if not self.is_connected():
            raise RuntimeError("Neo4j未连接")
        
        def write_tx(tx):
            result = tx.run(query, parameters or {})
            return [record.data() for record in result]
        
        try:
            with self.driver.session(database=self.config.database) as session:
                return session.write_transaction(write_tx)
                
        except Neo4jError as e:
            logger.error(f"执行写事务失败: {e}")
            raise
    
    def get_database_info(self) -> Dict[str, Any]:
        """获取数据库信息"""
        queries = {
            'node_count': "MATCH (n) RETURN count(n) as count",
            'relationship_count': "MATCH ()-[r]->() RETURN count(r) as count",
            'address_count': "MATCH (a:Address) RETURN count(a) as count",
            'transaction_count': "MATCH (t:Transaction) RETURN count(t) as count",
            'constraints': "SHOW CONSTRAINTS",
            'indexes': "SHOW INDEXES"
        }
        
        info = {}
        for key, query in queries.items():
            try:
                if key in ['constraints', 'indexes']:
                    info[key] = self.execute_query(query)
                else:
                    result = self.execute_query(query)
                    info[key] = result[0]['count'] if result else 0
            except Exception as e:
                logger.warning(f"获取{key}信息失败: {e}")
                info[key] = "Error"
        
        return info
    
    def __enter__(self):
        """上下文管理器入口"""
        if not self.is_connected():
            self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.disconnect()

# 全局连接管理器实例
_connection_manager = None

def get_connection_manager() -> Neo4jConnectionManager:
    """获取全局连接管理器"""
    global _connection_manager
    if _connection_manager is None:
        _connection_manager = Neo4jConnectionManager()
    return _connection_manager

def execute_cypher_file(file_path: str, connection_manager: Optional[Neo4jConnectionManager] = None) -> None:
    """执行Cypher文件"""
    manager = connection_manager or get_connection_manager()
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 分割查询（简单的分割方式）
        queries = [q.strip() for q in content.split(';') if q.strip() and not q.strip().startswith('//')]
        
        logger.info(f"执行Cypher文件: {file_path}, 包含 {len(queries)} 个查询")
        
        for i, query in enumerate(queries):
            try:
                if query:
                    manager.execute_query(query)
                    logger.debug(f"查询 {i+1} 执行成功")
            except Exception as e:
                logger.warning(f"查询 {i+1} 执行失败: {e}")
                
    except Exception as e:
        logger.error(f"执行Cypher文件失败: {e}")
        raise