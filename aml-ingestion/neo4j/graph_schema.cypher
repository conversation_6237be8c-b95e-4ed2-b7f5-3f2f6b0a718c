// ===================================================================
// 区块链反洗钱 (AML) 图数据库模式定义
// Real-Time Blockchain AML Graph Database Schema
// ===================================================================

// ===================
// 1. 清理现有数据 (开发环境)
// ===================
// 注意：生产环境中请谨慎使用
// MATCH (n) DETACH DELETE n;

// ===================
// 2. 创建约束 (Constraints)
// ===================

// 地址节点唯一性约束
CREATE CONSTRAINT address_unique IF NOT EXISTS 
FOR (a:Address) REQUIRE a.address IS UNIQUE;

// 交易节点唯一性约束  
CREATE CONSTRAINT transaction_unique IF NOT EXISTS
FOR (t:Transaction) REQUIRE t.tx_id IS UNIQUE;

// 区块节点唯一性约束
CREATE CONSTRAINT block_unique IF NOT EXISTS
FOR (b:Block) REQUIRE b.number IS UNIQUE;

// 区块哈希唯一性约束
CREATE CONSTRAINT block_hash_unique IF NOT EXISTS
FOR (b:Block) REQUIRE b.hash IS UNIQUE;

// ===================
// 3. 创建索引 (Indexes)
// ===================

// 地址相关索引
CREATE INDEX address_risk_score IF NOT EXISTS
FOR (a:Address) ON (a.risk_score);

CREATE INDEX address_balance IF NOT EXISTS  
FOR (a:Address) ON (a.balance);

CREATE INDEX address_last_seen IF NOT EXISTS
FOR (a:Address) ON (a.last_seen);

// 交易相关索引
CREATE INDEX transaction_timestamp IF NOT EXISTS
FOR (t:Transaction) ON (t.timestamp);

CREATE INDEX transaction_value IF NOT EXISTS
FOR (t:Transaction) ON (t.value_eth);

CREATE INDEX transaction_block IF NOT EXISTS
FOR (t:Transaction) ON (t.block_number);

CREATE INDEX transaction_gas_used IF NOT EXISTS
FOR (t:Transaction) ON (t.gas_used);

// 区块相关索引
CREATE INDEX block_timestamp IF NOT EXISTS
FOR (b:Block) ON (b.timestamp);

// 复合索引用于时间范围查询
CREATE INDEX transaction_time_value IF NOT EXISTS
FOR (t:Transaction) ON (t.timestamp, t.value_eth);

// ===================
// 4. 节点标签和属性定义
// ===================

// Address节点属性说明:
// - address: 以太坊地址 (唯一标识)
// - balance: 当前余额 (ETH)
// - total_received: 累计接收金额
// - total_sent: 累计发送金额  
// - transaction_count: 交易次数
// - first_seen: 首次出现时间戳
// - last_seen: 最后活动时间戳
// - risk_score: 风险评分 (0-100)
// - risk_level: 风险等级 (LOW/MEDIUM/HIGH/CRITICAL)
// - is_contract: 是否为合约地址
// - labels: AML标签数组 ["exchange", "mixer", "gambling"]

// Transaction节点属性说明:
// - tx_id: 交易哈希 (唯一标识)
// - from_addr: 发送方地址
// - to_addr: 接收方地址 (可为null，合约创建交易)
// - value_eth: 交易金额 (ETH)
// - gas_price: Gas价格 (Gwei)
// - gas_used: 实际使用的Gas
// - gas_limit: Gas限制
// - block_number: 区块号
// - timestamp: 交易时间戳
// - status: 交易状态 (1=成功, 0=失败)
// - is_suspicious: 是否被标记为可疑
// - suspicion_reasons: 可疑原因数组

// Block节点属性说明:
// - number: 区块号 (唯一标识)
// - hash: 区块哈希 (唯一标识)
// - timestamp: 区块时间戳
// - transaction_count: 区块内交易数量

// ===================
// 5. 关系类型定义
// ===================

// SENDS关系: (Address)-[:SENDS]->(Transaction)
// 属性: value_eth, timestamp, gas_fee

// RECEIVES关系: (Transaction)-[:RECEIVES]->(Address) 
// 属性: value_eth, timestamp

// IN_BLOCK关系: (Transaction)-[:IN_BLOCK]->(Block)
// 属性: position (交易在区块中的位置)

// TRANSACTS_WITH关系: (Address)-[:TRANSACTS_WITH]->(Address)
// 聚合关系，包含两个地址间的交易统计
// 属性: 
// - total_value: 总交易金额
// - transaction_count: 交易次数  
// - first_transaction: 首次交易时间
// - last_transaction: 最后交易时间
// - avg_value: 平均交易金额
// - risk_level: 关系风险等级

// SIMILAR_PATTERN关系: (Address)-[:SIMILAR_PATTERN]->(Address)
// 用于连接具有相似交易模式的地址
// 属性: similarity_score, pattern_type

// ===================
// 6. AML特定标签和属性
// ===================

// 高风险地址标签
// :Mixer - 混币服务
// :Exchange - 交易所  
// :Gambling - 赌博平台
// :DarkMarket - 暗网市场
// :Ransomware - 勒索软件
// :Phishing - 钓鱼攻击
// :Sanctioned - 制裁名单

// 交易模式标签
// :SuspiciousTransaction - 可疑交易
// :HighValue - 大额交易
// :RapidFire - 快速连续交易
// :RoundAmount - 整数金额交易
// :Layering - 分层交易

// ===================
// 7. 示例数据验证查询
// ===================

// 验证约束和索引创建
SHOW CONSTRAINTS;
SHOW INDEXES;

// 统计节点和关系数量的查询模板
// MATCH (a:Address) RETURN count(a) as address_count;
// MATCH (t:Transaction) RETURN count(t) as transaction_count;  
// MATCH (b:Block) RETURN count(b) as block_count;
// MATCH ()-[r]->() RETURN type(r) as relationship_type, count(r) as count ORDER BY count DESC;

// ===================
// 8. 性能优化建议
// ===================

// 定期维护命令:
// CALL db.indexes(); // 检查索引使用情况
// CALL apoc.stats.db(); // 数据库统计信息
// CALL db.schema.visualization(); // 可视化模式

// 内存和性能配置建议:
// - 确保有足够的页缓存存储图数据
// - 使用批量导入减少事务开销
// - 定期运行 CALL db.clearQueryCaches(); 清理查询缓存

// ===================
// 9. 数据质量约束
// ===================

// 可选的数据质量约束
// CREATE CONSTRAINT address_valid IF NOT EXISTS
// FOR (a:Address) REQUIRE a.address =~ '0x[a-fA-F0-9]{40}';

// CREATE CONSTRAINT transaction_value_positive IF NOT EXISTS  
// FOR (t:Transaction) REQUIRE t.value_eth >= 0;

// CREATE CONSTRAINT risk_score_range IF NOT EXISTS
// FOR (a:Address) REQUIRE a.risk_score >= 0 AND a.risk_score <= 100;

// ===================
// 10. 扩展功能准备
// ===================

// 为机器学习特征工程预留的节点属性:
// - ml_features: JSON格式的特征向量
// - cluster_id: 聚类分析结果
// - anomaly_score: 异常检测分数
// - community_id: 社区检测结果

// 为时间序列分析预留:
// - daily_volume: 日交易量
// - weekly_pattern: 周交易模式
// - velocity_score: 资金流动速度评分

RETURN "区块链AML图数据库模式创建完成！" as status;