#!/bin/bash
# Neo4j Setup and Validation Script
# =================================

echo "🚀 Setting up Neo4j for Blockchain AML Project"

# Create neo4j directory structure if it doesn't exist
mkdir -p neo4j/{schema,cypher_queries,ingestion,monitoring}

# Install Python dependencies for Neo4j
echo "📦 Installing Python dependencies..."
pip install neo4j==5.15.0 python-dotenv

# Wait for Neo4j to be ready
echo "⏳ Waiting for Neo4j to be ready..."
max_attempts=30
attempt=1

while [ $attempt -le $max_attempts ]; do
    echo "🔍 Checking Neo4j connection (attempt $attempt/$max_attempts)..."
    
    if docker exec neo4j-graph-db cypher-shell -u neo4j -p blockchain-aml-2024 -d neo4j "RETURN 1;" &>/dev/null; then
        echo "✅ Neo4j is ready!"
        break
    fi
    
    if [ $attempt -eq $max_attempts ]; then
        echo "❌ Neo4j failed to start after $max_attempts attempts"
        exit 1
    fi
    
    sleep 5
    ((attempt++))
done

# Run schema initialization
echo "🏗️ Initializing Neo4j schema..."
python3 neo4j/schema/schema_validation.py

# Create basic AML investigation queries
echo "📋 Creating AML investigation query templates..."

cat > neo4j/cypher_queries/aml_patterns.cypher << 'EOF'
// =============================================================================
// AML PATTERN DETECTION QUERIES
// =============================================================================

// 1. HIGH-RISK TRANSACTION PATTERNS
// Find transactions with high anomaly scores in the last 24 hours
MATCH (tx:Transaction)
WHERE tx.anomaly_score > 0.8 
  AND tx.timestamp > datetime() - duration('P1D')
RETURN tx.tx_hash, tx.anomaly_score, tx.value_eth, tx.timestamp
ORDER BY tx.anomaly_score DESC
LIMIT 20;

// 2. RAPID MOVEMENT PATTERN
// Detect addresses involved in rapid successive transactions
MATCH (addr:Address)-[:SENDS]->(tx:Transaction)
WHERE tx.timestamp > datetime() - duration('PT1H')
WITH addr, count(tx) AS tx_count, collect(tx.timestamp) AS timestamps
WHERE tx_count >= 5
RETURN addr.address, tx_count, timestamps
ORDER BY tx_count DESC;

// 3. HIGH-VALUE CLUSTERING
// Find clusters of high-value transactions between suspicious addresses
MATCH (addr1:Address)-[t:TRANSFERS]->(addr2:Address)
WHERE addr1.is_suspicious = true 
  AND t.value_eth > 10.0
  AND t.timestamp > datetime() - duration('P7D')
RETURN addr1.address, addr2.address, t.value_eth, t.timestamp
ORDER BY t.value_eth DESC;

// 4. CIRCULAR FLOW DETECTION
// Detect potential circular money flows (A -> B -> C -> A)
MATCH path = (start:Address)-[:TRANSFERS*2..4]->(start)
WHERE start.risk_score > 0.5
RETURN start.address, length(path) AS path_length, 
       [n IN nodes(path) | n.address] AS flow_path
LIMIT 10;

// 5. MIXING SERVICE DETECTION
// Find addresses that receive from many sources and send to many destinations
MATCH (addr:Address)
OPTIONAL MATCH (sender)-[:TRANSFERS]->(addr)
OPTIONAL MATCH (addr)-[:TRANSFERS]->(receiver)
WITH addr, count(DISTINCT sender) AS in_degree, count(DISTINCT receiver) AS out_degree
WHERE in_degree > 10 AND out_degree > 10
RETURN addr.address, addr.risk_score, in_degree, out_degree
ORDER BY (in_degree + out_degree) DESC;
EOF

cat > neo4j/cypher_queries/investigation_queries.cypher << 'EOF'
// =============================================================================
// INVESTIGATION AND COMPLIANCE QUERIES
// =============================================================================

// 1. ADDRESS INVESTIGATION
// Get complete transaction history for a specific address
MATCH (addr:Address {address: $target_address})
OPTIONAL MATCH (addr)-[r:SENDS|RECEIVES]-(tx:Transaction)
OPTIONAL MATCH (addr)-[t:TRANSFERS]-(other:Address)
RETURN addr, collect(DISTINCT tx) AS transactions, 
       collect(DISTINCT {address: other.address, relationship: type(t), value: t.value_eth}) AS connections
LIMIT 1;

// 2. TRANSACTION TRACE
// Trace the flow of funds from a specific transaction
MATCH (start_tx:Transaction {tx_hash: $tx_hash})
MATCH path = (start_tx)-[:RECEIVES]->(addr:Address)-[:TRANSFERS*1..5]->(end_addr:Address)
RETURN path, length(path) AS hop_count,
       [n IN nodes(path) WHERE n:Address | n.address] AS address_chain
ORDER BY hop_count;

// 3. TIME-BASED ANALYSIS
// Analyze transaction patterns in a specific time window
MATCH (tx:Transaction)
WHERE tx.timestamp >= datetime($start_time) 
  AND tx.timestamp <= datetime($end_time)
WITH tx.is_flagged AS flagged, count(*) AS tx_count, 
     sum(tx.value_eth) AS total_volume, avg(tx.anomaly_score) AS avg_anomaly
RETURN flagged, tx_count, total_volume, avg_anomaly;

// 4. RISK ASSESSMENT SUMMARY
// Generate risk assessment for compliance reporting
MATCH (addr:Address)
WHERE addr.risk_score > 0.0
WITH addr.risk_score AS risk_category, count(*) AS address_count
ORDER BY risk_category DESC
RETURN 
  CASE 
    WHEN risk_category >= 0.8 THEN 'Critical'
    WHEN risk_category >= 0.6 THEN 'High' 
    WHEN risk_category >= 0.4 THEN 'Medium'
    WHEN risk_category >= 0.2 THEN 'Low'
    ELSE 'Minimal'
  END AS risk_level,
  address_count;

// 5. ALERT MANAGEMENT
// Get open alerts with investigation status
MATCH (alert:Alert)
WHERE alert.status = 'open'
OPTIONAL MATCH (alert)-[:INVOLVES]->(addr:Address)
OPTIONAL MATCH (alert)-[:TRIGGERED_BY]->(tx:Transaction)
RETURN alert.alert_id, alert.alert_type, alert.severity, 
       alert.created_at, collect(DISTINCT addr.address) AS involved_addresses,
       collect(DISTINCT tx.tx_hash) AS trigger_transactions
ORDER BY alert.created_at DESC;
EOF

echo "✅ AML query templates created"

# Test basic Neo4j operations
echo "🧪 Testing Neo4j operations..."

# Test connection and basic query
if docker exec neo4j-graph-db cypher-shell -u neo4j -p blockchain-aml-2024 -d neo4j "MATCH (n) RETURN count(n) AS node_count;" 2>/dev/null; then
    echo "✅ Neo4j basic operations test passed"
else
    echo "❌ Neo4j basic operations test failed"
    exit 1
fi

# Show schema information
echo "📊 Neo4j Schema Information:"
docker exec neo4j-graph-db cypher-shell -u neo4j -p blockchain-aml-2024 -d neo4j "CALL db.schema.visualization();" 2>/dev/null || echo "Schema visualization not available"

# Show constraints and indexes
echo "🔍 Constraints:"
docker exec neo4j-graph-db cypher-shell -u neo4j -p blockchain-aml-2024 -d neo4j "SHOW CONSTRAINTS;" 2>/dev/null

echo "📇 Indexes:"
docker exec neo4j-graph-db cypher-shell -u neo4j -p blockchain-aml-2024 -d neo4j "SHOW INDEXES;" 2>/dev/null

echo ""
echo "🎉 Neo4j Setup Complete!"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "📍 Access Neo4j Browser: http://localhost:7474"
echo "🔐 Username: neo4j"
echo "🔑 Password: blockchain-aml-2024"
echo "📊 Database: neo4j"
echo ""
echo "🔄 Next Steps:"
echo "1. Verify schema in Neo4j Browser"
echo "2. Test AML investigation queries"
echo "3. Proceed to Step 3: Set up Neo4j Spark Connector"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"