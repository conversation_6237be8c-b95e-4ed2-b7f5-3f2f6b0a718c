#!/bin/bash

# AML系统优化启动脚本
# AML System Optimization Startup Script

echo "🚀 启动AML系统监控和优化..."

# 检查Docker和Docker Compose
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 创建监控目录
mkdir -p monitoring/grafana/{provisioning,dashboards}
mkdir -p monitoring/grafana/provisioning/{dashboards,datasources}

# 检查主服务状态
echo "📊 检查主服务状态..."
docker-compose ps

# 启动监控服务
echo "📈 启动监控服务..."
docker-compose -f monitoring/docker-compose.monitoring.yml up -d

# 等待服务启动
echo "⏳ 等待监控服务启动..."
sleep 30

# 检查监控服务状态
echo "✅ 检查监控服务状态..."
docker-compose -f monitoring/docker-compose.monitoring.yml ps

# 显示访问信息
echo ""
echo "🎯 监控服务访问信息:"
echo "   Grafana仪表板: http://localhost:3000 (admin/blockchain-aml-2024)"
echo "   Prometheus: http://localhost:9090"
echo "   AlertManager: http://localhost:9093"
echo "   Node Exporter: http://localhost:9100"
echo ""

# 启动性能优化器（可选）
read -p "是否启动自动性能优化器? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🔧 启动性能优化器..."
    python3 monitoring/performance_optimizer.py &
    echo "优化器已在后台启动"
fi

echo "✨ 监控和优化系统启动完成！"