package com.blockchain.aml.cep;

import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.functions.FilterFunction;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.cep.CEP;
import org.apache.flink.cep.PatternSelectFunction;
import org.apache.flink.cep.PatternStream;
import org.apache.flink.cep.pattern.Pattern;
import org.apache.flink.cep.pattern.conditions.SimpleCondition;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.assigners.SlidingEventTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.table.api.EnvironmentSettings;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.util.Collector;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;

import java.time.Duration;
import java.util.List;
import java.util.Map;

/**
 * Apache Flink CEP Processor for Blockchain AML
 * Implements 85%+ benign transaction filtering using SQL-based pattern detection
 * and statistical anomaly analysis
 */
public class FlinkCEPProcessor {
    
    private static final String KAFKA_BOOTSTRAP_SERVERS = "kafka:29092";
    private static final String INPUT_TOPIC = "ethereum-transactions";
    private static final String OUTPUT_TOPIC = "filtered-transactions";
    private static final String SUSPICIOUS_ALERTS_TOPIC = "suspicious-alerts";
    
    // Statistical thresholds for benign classification
    private static final double NORMAL_VALUE_THRESHOLD_ETH = 10.0;
    private static final int NORMAL_GAS_LIMIT = 21000;
    private static final int RAPID_TX_THRESHOLD = 5; // transactions per minute
    private static final double HIGH_VALUE_THRESHOLD_ETH = 100.0;
    
    public static void main(String[] args) throws Exception {
        
        // Set up the execution environment
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(2); // Reduced parallelism to match available resources
        
        // Enable checkpointing for fault tolerance
        env.enableCheckpointing(30000); // 30 second intervals
        
        // Create table environment for SQL-based processing
        EnvironmentSettings settings = EnvironmentSettings.newInstance()
                .inStreamingMode()
                .build();
        StreamTableEnvironment tableEnv = StreamTableEnvironment.create(env, settings);
        
        System.out.println("Starting Flink CEP Processor for Blockchain AML...");
        
        // Step 1: Set up Kafka source for transaction ingestion
        KafkaSource<String> kafkaSource = createKafkaSource();
        
        // Step 2: Parse JSON transactions and extract features
        DataStream<TransactionEvent> transactionStream = env
                .fromSource(kafkaSource, WatermarkStrategy.noWatermarks(), "Kafka Source")
                .map(new TransactionParser())
                .name("Transaction Parser");
        
        // Step 3: Apply statistical filters to remove obvious benign transactions
        DataStream<TransactionEvent> statisticallyFiltered = transactionStream
                .filter(new StatisticalBenignFilter())
                .name("Statistical Benign Filter");
        
        // Step 4: Apply SQL-based pattern detection for advanced filtering
        DataStream<TransactionEvent> patternFiltered = applySQLPatternFiltering(
                statisticallyFiltered, tableEnv);
        
        // Step 5: Apply CEP patterns for complex suspicious behavior detection
        DataStream<SuspiciousAlert> suspiciousPatterns = applyCEPPatterns(patternFiltered);
        
        // Step 6: Route filtered transactions to next processing stage
        setupOutputSinks(patternFiltered, suspiciousPatterns);
        
        // Execute the pipeline
        env.execute("Blockchain AML CEP Processor");
    }
    
    /**
     * Creates Kafka source with optimized configuration for high throughput
     */
    private static KafkaSource<String> createKafkaSource() {
        return KafkaSource.<String>builder()
                .setBootstrapServers(KAFKA_BOOTSTRAP_SERVERS)
                .setTopics(INPUT_TOPIC)
                .setGroupId("cep-processor-group")
                .setStartingOffsets(OffsetsInitializer.earliest())
                .setValueOnlyDeserializer(new SimpleStringSchema())
                .build();
    }
    
    /**
     * Statistical filter that removes obviously benign transactions
     * This is the first line of defense, targeting 70-80% filtering efficiency
     */
    public static class StatisticalBenignFilter implements FilterFunction<TransactionEvent> {
        @Override
        public boolean filter(TransactionEvent tx) throws Exception {
            // Filter small value transactions during normal hours (likely routine)
            if (tx.valueEth < 0.01 && isNormalBusinessHours(tx.timestamp)) {
                return false; // Filter out (benign)
            }
            
            // Filter standard gas limit transactions (likely normal transfers)
            if (tx.gasUsed <= NORMAL_GAS_LIMIT && tx.valueEth < NORMAL_VALUE_THRESHOLD_ETH) {
                return false; // Filter out (benign)
            }
            
            // Filter transactions to/from known exchange addresses (could be expanded)
            if (isKnownExchangeAddress(tx.fromAddr) || isKnownExchangeAddress(tx.toAddr)) {
                return false; // Filter out (benign)
            }
            
            // Keep transaction for further analysis
            return true;
        }
        
        private boolean isNormalBusinessHours(long timestamp) {
            // Simple heuristic - can be enhanced with timezone awareness
            long hour = (timestamp / 3600) % 24;
            return hour >= 6 && hour <= 22; // 6 AM to 10 PM
        }
        
        private boolean isKnownExchangeAddress(String address) {
            // Placeholder for exchange address whitelist
            // In production, this would check against a maintained list
            return address != null && address.toLowerCase().contains("exchange");
        }
    }
    
    /**
     * Applies SQL-based pattern filtering using Flink Table API
     * Targets additional 10-15% filtering through complex pattern analysis
     */
    private static DataStream<TransactionEvent> applySQLPatternFiltering(
            DataStream<TransactionEvent> stream, StreamTableEnvironment tableEnv) {
        
        // Create explicit schema for the table registration
        Schema schema = Schema.newBuilder()
            .column("txId", DataTypes.STRING())
            .column("fromAddr", DataTypes.STRING())
            .column("toAddr", DataTypes.STRING())
            .column("valueEth", DataTypes.DOUBLE())
            .column("blockNumber", DataTypes.INT())
            .column("timestamp", DataTypes.BIGINT())
            .column("gasPrice", DataTypes.INT())
            .column("gasUsed", DataTypes.INT())
            .build();
        
        // Register the stream as a table with explicit schema
        tableEnv.createTemporaryView("transactions", stream, schema);
        
        // Fixed SQL query with correct column references
        String suspiciousPatternSQL = "SELECT * " +
            "FROM transactions " +
            "WHERE NOT ( " +
                "(valueEth IN (1.0, 5.0, 10.0, 50.0, 100.0) AND gasUsed = 21000) OR " +
                "(valueEth < 0.1 AND gasUsed > 100000) OR " +
                "(gasUsed BETWEEN 35000 AND 70000 AND valueEth = 0.0) OR " +
                "(toAddr IS NULL AND gasUsed > 200000) " +
            ")";
            
        Table suspiciousTable = tableEnv.sqlQuery(suspiciousPatternSQL);
        
        return tableEnv.toDataStream(suspiciousTable, TransactionEvent.class);
    }
    
    /**
     * Applies Complex Event Processing patterns to detect sophisticated suspicious behavior
     * This catches the remaining sophisticated patterns that simple filters miss
     */
    private static DataStream<SuspiciousAlert> applyCEPPatterns(
            DataStream<TransactionEvent> filteredStream) {
        
        // Pattern 1: Rapid sequence of transactions (possible layering)
        Pattern<TransactionEvent, ?> rapidTransactionPattern = Pattern
                .<TransactionEvent>begin("first")
                .where(new SimpleCondition<TransactionEvent>() {
                    @Override
                    public boolean filter(TransactionEvent tx) {
                        return tx.valueEth > 1.0; // Significant value
                    }
                })
                .next("second")
                .where(new SimpleCondition<TransactionEvent>() {
                    @Override
                    public boolean filter(TransactionEvent tx) {
                        return tx.valueEth > 1.0;
                    }
                })
                .next("third")
                .where(new SimpleCondition<TransactionEvent>() {
                    @Override
                    public boolean filter(TransactionEvent tx) {
                        return tx.valueEth > 1.0;
                    }
                })
                .within(Time.minutes(2)); // Within 2 minutes
        
        // Pattern 2: High-value transaction followed by rapid splitting
        Pattern<TransactionEvent, ?> splittingPattern = Pattern
                .<TransactionEvent>begin("large")
                .where(new SimpleCondition<TransactionEvent>() {
                    @Override
                    public boolean filter(TransactionEvent tx) {
                        return tx.valueEth > HIGH_VALUE_THRESHOLD_ETH;
                    }
                })
                .followedBy("splits")
                .where(new SimpleCondition<TransactionEvent>() {
                    @Override
                    public boolean filter(TransactionEvent tx) {
                        return tx.valueEth < HIGH_VALUE_THRESHOLD_ETH / 2;
                    }
                })
                .times(3, 10) // 3 to 10 split transactions
                .within(Time.minutes(5));
        
        // Apply patterns and generate alerts
        PatternStream<TransactionEvent> rapidPatternStream = CEP.pattern(
                filteredStream.keyBy(tx -> tx.fromAddr), rapidTransactionPattern);
        
        PatternStream<TransactionEvent> splittingPatternStream = CEP.pattern(
                filteredStream.keyBy(tx -> tx.fromAddr), splittingPattern);
        
        // Generate alerts from detected patterns
        DataStream<SuspiciousAlert> rapidAlerts = rapidPatternStream
                .select(new RapidTransactionAlertFunction());
        
        DataStream<SuspiciousAlert> splittingAlerts = splittingPatternStream
                .select(new SplittingPatternAlertFunction());
        
        return rapidAlerts.union(splittingAlerts);
    }
    
    /**
     * Sets up output sinks for filtered transactions and suspicious alerts
     */
    private static void setupOutputSinks(DataStream<TransactionEvent> filteredTransactions,
                                       DataStream<SuspiciousAlert> suspiciousAlerts) {
        
        // Sink for filtered transactions (will go to graph construction)
        KafkaSink<String> filteredSink = KafkaSink.<String>builder()
                .setBootstrapServers(KAFKA_BOOTSTRAP_SERVERS)
                .setRecordSerializer(
                    org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema
                        .builder()
                        .setTopic(OUTPUT_TOPIC)
                        .setValueSerializationSchema(new SimpleStringSchema())
                        .build())
                .build();
        
        // Sink for suspicious alerts (immediate investigation)
        KafkaSink<String> alertsSink = KafkaSink.<String>builder()
                .setBootstrapServers(KAFKA_BOOTSTRAP_SERVERS)
                .setRecordSerializer(
                    org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema
                        .builder()
                        .setTopic(SUSPICIOUS_ALERTS_TOPIC)
                        .setValueSerializationSchema(new SimpleStringSchema())
                        .build())
                .build();
        
        // Convert to JSON and sink
        filteredTransactions
                .map(tx -> tx.toJson())
                .sinkTo(filteredSink)
                .name("Filtered Transactions Sink");
        
        suspiciousAlerts
                .map(alert -> alert.toJson())
                .sinkTo(alertsSink)
                .name("Suspicious Alerts Sink");
    }
    
    // Supporting classes for pattern detection
    public static class TransactionParser implements MapFunction<String, TransactionEvent> {
        private final ObjectMapper objectMapper = new ObjectMapper();
        
        @Override
        public TransactionEvent map(String jsonString) throws Exception {
            JsonNode json = objectMapper.readTree(jsonString);
            return new TransactionEvent(
                json.get("txId").asText(),
                json.get("fromAddr").asText(),
                json.get("toAddr") != null ? json.get("toAddr").asText() : null,
                json.get("valueEth").asDouble(),
                json.get("blockNumber").asInt(),
                json.get("timestamp").asLong(),
                json.get("gasPrice").asInt(),
                json.get("gasUsed").asInt()
            );
        }
    }
    
    public static class RapidTransactionAlertFunction 
            implements PatternSelectFunction<TransactionEvent, SuspiciousAlert> {
        @Override
        public SuspiciousAlert select(Map<String, List<TransactionEvent>> pattern) {
            List<TransactionEvent> rapidTxs = pattern.get("first");
            rapidTxs.addAll(pattern.get("second"));
            rapidTxs.addAll(pattern.get("third"));
            
            return new SuspiciousAlert(
                "RAPID_TRANSACTION_PATTERN",
                rapidTxs.get(0).fromAddr,
                "Multiple high-value transactions in rapid succession",
                0.8, // Confidence score
                System.currentTimeMillis()
            );
        }
    }
    
    public static class SplittingPatternAlertFunction 
            implements PatternSelectFunction<TransactionEvent, SuspiciousAlert> {
        @Override
        public SuspiciousAlert select(Map<String, List<TransactionEvent>> pattern) {
            List<TransactionEvent> largeTx = pattern.get("large");
            List<TransactionEvent> splits = pattern.get("splits");
            
            return new SuspiciousAlert(
                "FUND_SPLITTING_PATTERN", 
                largeTx.get(0).fromAddr,
                "Large transaction followed by fund splitting behavior",
                0.75, // Confidence score
                System.currentTimeMillis()
            );
        }
    }
    
    // Data classes
    public static class TransactionEvent {
        public String txId;
        public String fromAddr;
        public String toAddr;
        public double valueEth;
        public int blockNumber;
        public long timestamp;
        public int gasPrice;
        public int gasUsed;
        
        // Default constructor required for Flink serialization
        public TransactionEvent() {}
        
        public TransactionEvent(String txId, String fromAddr, String toAddr, 
                              double valueEth, int blockNumber, long timestamp,
                              int gasPrice, int gasUsed) {
            this.txId = txId;
            this.fromAddr = fromAddr;
            this.toAddr = toAddr;
            this.valueEth = valueEth;
            this.blockNumber = blockNumber;
            this.timestamp = timestamp;
            this.gasPrice = gasPrice;
            this.gasUsed = gasUsed;
        }
        
        public String toJson() {
            return String.format(
            "{\"txId\":\"%s\",\"fromAddr\":\"%s\",\"toAddr\":%s," +
                "\"valueEth\":%.8f,\"blockNumber\":%d,\"timestamp\":%d," +
                "\"gasPrice\":%d,\"gasUsed\":%d}",
                txId, fromAddr, 
                toAddr != null ? ("\"" + toAddr + "\"") : "null",
                valueEth, blockNumber, timestamp, gasPrice, gasUsed
            );
        }
    }
    
    public static class SuspiciousAlert {
        public String patternType;
        public String suspiciousAddress;
        public String description;
        public double confidenceScore;
        public long alertTimestamp;
        
        // Default constructor required for Flink serialization
        public SuspiciousAlert() {}
        
        public SuspiciousAlert(String patternType, String suspiciousAddress, 
                             String description, double confidenceScore, long alertTimestamp) {
            this.patternType = patternType;
            this.suspiciousAddress = suspiciousAddress;
            this.description = description;
            this.confidenceScore = confidenceScore;
            this.alertTimestamp = alertTimestamp;
        }
        
        public String toJson() {
            return String.format(
                "{\"patternType\":\"%s\",\"suspiciousAddress\":\"%s\"," +
                "\"description\":\"%s\",\"confidenceScore\":%.2f,\"alertTimestamp\":%d}",
                patternType, suspiciousAddress, description, confidenceScore, alertTimestamp
            );
        }
    }
}