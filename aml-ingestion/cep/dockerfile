# Multi-stage build for CEP Processor
FROM maven:3.8.3-openjdk-11-slim AS builder

# Set working directory for build
WORKDIR /build

# Copy Maven configuration files first (for better layer caching)
COPY pom.xml .

# Download dependencies (this layer will be cached if pom.xml doesn't change)
RUN mvn dependency:go-offline -B

COPY src ./src

# Build the application
RUN mvn clean package -DskipTests -B

# Verify the JAR was built successfully
RUN ls -la target/ && \
    test -f target/flink-cep-processor-*.jar || (echo "JAR file not found!" && exit 1)

# Production stage - use Flink base image
FROM apache/flink:1.17.2-java11

# Install additional tools needed for the startup script
USER root
RUN apt-get update && apt-get install -y \
    curl \
    jq \
    kafkacat \
    netcat-traditional \
    file \
    && rm -rf /var/lib/apt/lists/*

# Create application directory
RUN mkdir -p /opt/cep-processor

# Copy the built JAR from builder stage
COPY --from=builder /build/target/flink-cep-processor-*.jar /opt/flink/lib/flink-cep-processor.jar

# Copy startup script with explicit line ending conversion
COPY startup.sh /opt/cep-processor/startup.sh

# Fix potential line ending issues and set permissions
RUN sed -i 's/\r$//' /opt/cep-processor/startup.sh && \
    chmod +x /opt/cep-processor/startup.sh && \
    chown flink:flink /opt/cep-processor/startup.sh

# Verify the script is executable and has correct format
RUN file /opt/cep-processor/startup.sh && \
    head -n 1 /opt/cep-processor/startup.sh

# Copy deployment script as well
COPY deploy-cep-job.sh /opt/cep-processor/deploy-cep-job.sh
RUN sed -i 's/\r$//' /opt/cep-processor/deploy-cep-job.sh && \
    chmod +x /opt/cep-processor/deploy-cep-job.sh && \
    chown flink:flink /opt/cep-processor/deploy-cep-job.sh

# Set environment variables
ENV FLINK_HOME=/opt/flink
ENV FLINK_JOBMANAGER_HOST=flink-jobmanager
ENV FLINK_JOBMANAGER_PORT=8081
ENV KAFKA_BOOTSTRAP_SERVERS=kafka:29092

# Switch back to flink user for security
USER flink

# Health check to ensure the container is ready
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=5 \
    CMD curl -f http://${FLINK_JOBMANAGER_HOST}:${FLINK_JOBMANAGER_PORT}/overview || exit 1

# Set working directory
WORKDIR /opt/cep-processor

# Default command runs the startup script
CMD ["./startup.sh"]