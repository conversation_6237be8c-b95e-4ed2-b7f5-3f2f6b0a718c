import asyncio
import json
import os
import time
import logging
from typing import Set, Dict, Any
from web3 import Web3
from confluent_kafka import Producer, KafkaException
from dataclasses import dataclass

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class TransactionRecord:
    txId: str
    fromAddr: str
    toAddr: str
    valueEth: float
    blockNumber: int
    timestamp: int
    gasPrice: int
    gasUsed: int
    
class IngestionMetrics:
    def __init__(self):
        self.total_transactions: int = 0
        self.blocks_processed: int = 0
        self.errors_count: int = 0
        self.start_time: float = time.time()

class EnhancedInfuraIngestion:
    def __init__(self):
        logger.info("Initializing EnhancedInfuraIngestion...")
        
        # Print all environment variables for debugging
        logger.info("Environment variables:")
        for key, value in os.environ.items():
            if 'INFURA' in key or 'KAFKA' in key:
                logger.info(f"  {key}={value}")
        
        self.infura_url = os.environ.get("INFURA_URL")
        self.kafka_bootstrap = os.getenv("KAFKA_BOOTSTRAP_SERVERS", "kafka:29092")
        
        logger.info(f"Using Infura URL: {self.infura_url}")
        logger.info(f"Using Kafka bootstrap: {self.kafka_bootstrap}")
        
        # Initialize Web3 connection
        if not self.infura_url:
            logger.error("INFURA_URL environment variable is missing!")
            raise ValueError("INFURA_URL environment variable is required")
            
        try:
            self.w3 = Web3(Web3.HTTPProvider(self.infura_url))
            logger.info("Web3 instance created successfully")
        except Exception as e:
            logger.error(f"Failed to create Web3 instance: {e}")
            raise
        
        self.seen_transactions: Set[str] = set()
        self.last_request_time = 0
        self.min_request_interval = 0.2
        self.metrics = IngestionMetrics()

        # Validate configuration first
        logger.info("Validating configuration...")
        self.validate_config()
        
        # Then create producer after validation
        logger.info("Creating Kafka producer...")
        self.producer = self._create_producer()
        logger.info("Initialization completed successfully!")

    def validate_config(self):
        logger.info("Starting configuration validation...")
        
        if not self.infura_url:
            logger.error("INFURA_URL validation failed")
            raise ValueError("INFURA_URL environment variable required")
        
        try:
            logger.info("Testing Ethereum connection...")
            is_connected = self.w3.is_connected()
            logger.info(f"Ethereum connection status: {is_connected}")
            
            if not is_connected:
                logger.error("Cannot connect to Ethereum node")
                raise ConnectionError("Cannot connect to Ethereum node")
                
            # Test getting latest block number
            latest_block = self.w3.eth.block_number
            logger.info(f"Latest block number: {latest_block}")
            
        except Exception as e:
            logger.error(f"Ethereum connection check failed: {e}")
            raise

    async def _rate_limit(self):
        elapsed = time.time() - self.last_request_time
        if elapsed < self.min_request_interval:
            await asyncio.sleep(self.min_request_interval - elapsed)
        self.last_request_time = time.time()

    def _create_producer(self, retries=10, delay=3) -> Producer:
        logger.info(f"Creating Kafka producer with bootstrap servers: {self.kafka_bootstrap}")
        
        for attempt in range(1, retries + 1):
            try:
                logger.info(f"Kafka connection attempt {attempt}/{retries}")
                
                producer_config = {
                    'bootstrap.servers': self.kafka_bootstrap,
                    'enable.idempotence': True,
                    'acks': 'all',
                    'retries': 3,
                    'batch.size': 16384,
                    'linger.ms': 10
                }
                
                logger.debug(f"Producer config: {producer_config}")
                producer = Producer(producer_config)
                
                # Test connection by listing topics
                logger.info("Testing Kafka connection by listing topics...")
                topics = producer.list_topics(timeout=10)
                logger.info(f"Available topics: {list(topics.topics.keys())}")
                
                logger.info(f"Successfully connected to Kafka at {self.kafka_bootstrap} (attempt {attempt})")
                return producer
                
            except KafkaException as e:
                logger.warning(f"Kafka connection failed (attempt {attempt}/{retries}): {e}")
                if attempt < retries:
                    logger.info(f"Waiting {delay} seconds before retry...")
                    time.sleep(delay)
                else:
                    logger.error(f"Failed to connect to Kafka after {retries} attempts")
                    raise RuntimeError(f"Failed to connect to Kafka after {retries} attempts: {e}")
            except Exception as e:
                logger.error(f"Unexpected error during Kafka connection (attempt {attempt}/{retries}): {e}")
                if attempt < retries:
                    time.sleep(delay)
                else:
                    raise
    
    def _extract_transaction_data(self, tx, block) -> TransactionRecord:
        return TransactionRecord(
            txId=tx.hash.hex(),
            fromAddr=tx.get('from', ''),
            toAddr=tx.get('to') if tx.get('to') else None,
            valueEth=float(tx.get('value', 0)) / 10**18,
            blockNumber=block.number,
            timestamp=block.timestamp,
            gasPrice=tx.get('gasPrice', 0),
            gasUsed=tx.get('gas', 0)
        )
    
    def _delivery_callback(self, err, msg):
        if err:
            logger.error(f"Message delivery failed: {err}")
        else:
            logger.debug(f"Message delivered to {msg.topic()}[{msg.partition()}]")
    
    async def ingest_transactions(self):
        logger.info("Starting transaction ingestion...")
        
        while True:
            try:
                await self._rate_limit()
                
                # Get latest block with full transactions
                logger.debug("Fetching latest block...")
                block = self.w3.eth.get_block('latest', full_transactions=True)
                logger.info(f"Processing block {block.number} with {len(block.transactions)} transactions")
                
                new_transactions = 0
                
                for tx in block.transactions:
                    tx_hash = tx.hash.hex()
                    
                    # Deduplication check
                    if tx_hash not in self.seen_transactions:
                        self.seen_transactions.add(tx_hash)
                        
                        # Extract transaction data
                        tx_record = self._extract_transaction_data(tx, block)
                        
                        # Produce to Kafka
                        self.producer.produce(
                            'ethereum-transactions',
                            key=tx_hash,
                            value=json.dumps(tx_record.__dict__),
                            callback=self._delivery_callback
                        )
                        new_transactions += 1
                        self.metrics.total_transactions += 1
                
                # Flush producer
                self.producer.poll(0)
                
                if new_transactions > 0:
                    logger.info(f"Ingested {new_transactions} new transactions from block {block.number}")
                    self.metrics.blocks_processed += 1
                else:
                    logger.debug(f"No new transactions in block {block.number}")
                
                # Clean up old seen transactions (keep last 1000)
                if len(self.seen_transactions) > 1000:
                    self.seen_transactions = set(list(self.seen_transactions)[-1000:])
                    logger.debug("Cleaned up old transaction cache")
                
                if self.metrics.blocks_processed % 10 == 0 and self.metrics.blocks_processed > 0:
                    runtime = time.time() - self.metrics.start_time
                    logger.info(f"Metrics: {self.metrics.total_transactions} transactions, "
                            f"{self.metrics.blocks_processed} blocks, "
                            f"{self.metrics.errors_count} errors, "
                            f"Runtime: {runtime:.1f}s")

                await asyncio.sleep(2)
                
            except Exception as e:
                logger.error(f"Error during ingestion: {e}", exc_info=True)
                self.metrics.errors_count += 1
                logger.info("Waiting 5 seconds before retry...")
                await asyncio.sleep(5)
    
if __name__ == "__main__":
    try:
        logger.info("Starting application...")
        ingestion = EnhancedInfuraIngestion()
        logger.info("Running transaction ingestion...")
        asyncio.run(ingestion.ingest_transactions())
    except Exception as e:
        logger.error(f"Application failed to start: {e}", exc_info=True)
        exit(1)